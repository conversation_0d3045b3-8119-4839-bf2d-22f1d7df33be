<?php
//require_once '../includes/session.php';
include '../query/access.qry';
include '../query/students.qry';
include '../query/dashboard.qry';

$access = new ACCESS;
$student = new STUDENTS();
$dashboard = new DASHBOARD();

$useraccountsuaid = false; 
if (isset($_SESSION['uaid'])) {
    $useraccountsuaid = $access->get_useraccountsuaid($db1, $_SESSION['uaid']);
    if ($useraccountsuaid !== false && is_array($useraccountsuaid)) {
        $_SESSION['profile_picture'] = $useraccountsuaid['profilePicture'];
    }
}

if(isset($_POST['saveProfileChanges']))
{
    $newPassword = $_POST['newPassword'];
    if ($useraccountsuaid !== false && is_array($useraccountsuaid) && isset($useraccountsuaid['uaid'])) {
        $access->upd_useraccountsuaid($db1, $newPassword, $useraccountsuaid['uaid']);
        echo $_POST['newPassword'];
    }
}
?>



    <?php include '../assets/common/header.php'; ?>

<body>
    <!-- ======== Preloader =========== -->
    <!--<div id="preloader">
        <div class="spinner"></div>
    </div>-->
    <!-- ======== Preloader =========== -->
	<?php include '../assets/common/title.php'; ?>
	<div class="main-container" id="main-container"  >
	<!-- ======== sidebar-nav start =========== -->
    <?php include '../assets/common/sidebar.php'; ?>
	
	</div>
    <!-- ======== sidebar-nav end =========== -->

    <!-- ======== Main Wrapper Start =========== -->
    <main class="main-wrapper">
        <!-- ========== Header Start ========== -->
        <?php 
        // Pass user data to topbar
        require '../assets/common/topbar.php'; 
        ?>
        <!-- ========== Header End ========== -->

        <!-- ========== Main Content Start ========== -->
        <?php
        // Display Bootstrap Alert
        if (isset($_SESSION['alert'])) {
            echo '<div class="alert alert-' . $_SESSION['alert']['type'] . ' alert-dismissible fade show" role="alert">
                ' . $_SESSION['alert']['message'] . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>';
            unset($_SESSION['alert']);
        }
        
        // Pass page data to main content
        $page_title = "ITRC Admin Dashboard";
        $current_page = "Home";

        $allStudents = $student->get_all_students($db1);
        $totalStudents = count($allStudents);
        $activeEvents = $dashboard->get_active_events($db1);
        $totalUserAccounts = $dashboard->get_total_user_accounts($db1);

        $content = '';
      
        ?>
		<section class="section">
        <div class="container-fluid">
          <!-- ========== title-wrapper start ========== -->
          <div class="title-wrapper pt-30">
            <div class="row align-items-center">
              <div class="col-md-6">
                <div class="title">
                  <h2><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?></h2>
                </div>
              </div>
              <!-- end col -->
              <div class="col-md-6">
                <div class="breadcrumb-wrapper">
                  <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                      <li class="breadcrumb-item">
                        <a href="#0">Dashboard</a>
                      </li>
                      <li class="breadcrumb-item active" aria-current="page">
                       <?php echo isset($current_page) ? $current_page : ''; ?>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <!-- end col -->
            </div>
            <!-- end row -->
          </div>
		 <div class="row">
            <div class="col-xl-3 col-lg-4 col-sm-6">
              <div class="icon-card mb-30">
                <div class="icon purple">
                  <i class="lni lni-cart-full"></i>
                </div>
                <div class="content">
                  <h6 class="mb-10">Total Students</h6>
                  <h3 class="text-bold mb-10"><?php echo $totalStudents; ?></h3>
                  <!--<p class="text-sm text-success">
                    <i class="lni lni-arrow-up"></i> +2.00%
                    <span class="text-gray">(30 days)</span>
                  </p>-->
                </div>
              </div>
              <!-- End Icon Cart -->
            </div>
            <!-- End Col -->
            <div class="col-xl-3 col-lg-4 col-sm-6">
              <div class="icon-card mb-30">
                <div class="icon success">
                  <i class="lni lni-dollar"></i>
                </div>
                <div class="content">
                  <h6 class="mb-10">Active Events</h6>
                  <h3 class="text-bold mb-10"><?php echo $activeEvents; ?></h3>
                  <!--<p class="text-sm text-success">
                    <i class="lni lni-arrow-up"></i> +5.45%
                    <span class="text-gray">Increased</span>
                  </p>-->
                </div>
              </div>
              <!-- End Icon Cart -->
            </div>
            <!-- End Col -->
            <div class="col-xl-3 col-lg-4 col-sm-6">
              <div class="icon-card mb-30">
                <div class="icon primary">
                  <i class="lni lni-credit-cards"></i>
                </div>
                <div class="content">
                  <h6 class="mb-10">Total User Accounts</h6>
                  <h3 class="text-bold mb-10"><?php echo $totalUserAccounts; ?></h3>
                  <!--<p class="text-sm text-danger">
                    <i class="lni lni-arrow-down"></i> -2.00%
                    <span class="text-gray">Expense</span>
                  </p>-->
                </div>
              </div>
              <!-- End Icon Cart -->
            </div>
            <!-- End Col -->
            <div class="col-xl-3 col-lg-4 col-sm-6">
              <div class="icon-card mb-30">
                <div class="icon orange">
                  <i class="lni lni-user"></i>
                </div>
                <div class="content">
                  <h6 class="mb-10">Sample</h6>
                  <h3 class="text-bold mb-10">34567</h3>
                  <!--<p class="text-sm text-danger">
                    <i class="lni lni-arrow-down"></i> -25.00%
                    <span class="text-gray"> Earning</span>
                  </p>-->
                </div>
              </div>
              <!-- End Icon Cart -->
            </div>
            <!-- End Col -->
          </div>
		  <!-- Google Calendar -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card-style mb-10">
                    <div class="title d-flex flex-wrap justify-content-between align-items-center">
                        <div class="left">
                            <h6 class="text-medium mb-2">Calendar</h6>
                        </div>
                    </div>
                    <div class="calendar-container" style="width: 100%; min-height: 600px;">
                        <iframe src="https://calendar.google.com/calendar/embed?src=gmccruz%40donbosco.edu.ph&ctz=Asia%2FManila" 
                                style="border: 0; width: 100%; height: 600px;" 
                                frameborder="0" 
                                scrolling="no">
                        </iframe>
                    </div>
                </div>
            </div>
        </div>
          </div>
          </div>
		 <?
       // require '../assets/common/main.php'; 
        ?>
        <!-- ========== Main Content End ========== -->

        <!-- ========== Footer Start ========== -->
        <?php include '../assets/common/footer.php'; ?>
        <!-- ========== Footer End ========== -->
    </main>
	
    <!-- ======== Main Wrapper End =========== -->

    <!-- ========= Scripts =========== -->
    <?php include '../assets/common/scripts.php'; ?>
</body>
</html>
