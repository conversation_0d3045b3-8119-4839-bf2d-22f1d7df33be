<?php
class DASHBOARD {
    function get_active_events($db) {
        $stmt = $db->prepare("SELECT COUNT(*) as active_events FROM schedules WHERE DATE(schedule_date) = CURDATE() AND TIME(NOW()) BETWEEN start_time AND end_time");
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return intval($row['active_events'] ?? 0);
    }

    function get_total_user_accounts($db) {
        $stmt = $db->prepare("SELECT COUNT(*) as user_count FROM useraccounts");
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return intval($row['user_count'] ?? 0);
    }
}
?>