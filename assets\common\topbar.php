<?php 
//session_start();
include_once '../core/dbcon.ini';
include_once '../query/notifications.qry';

?>

	  
	  <!-- ========== header start ========== -->
		<header class="header" style="background-color:#EFBF04;">
        <div class="container-fluid h-100">
            <div class="row h-100">
                <div class="col-lg-3 col-md-4 col-6 h-100" style="background-color:#EFBF04;">
                <div class="header-left d-flex align-items-center h-100" style="background-color:#EFBF04;">
                    <div class="menu-toggle-btn me-3">
                    <button id="menu-toggle" class="main-btn primary-btn btn-hover" style="background-color:#1E2F97;">
                        <i class="lni lni-chevron-left me-2"></i> Menu
                    </button>
                </div>
                <!--<div class="header-search d-none d-md-flex">
                  <form action="#">
                    <input type="text" placeholder="Search..." />--
					<img src="../assets/images/logo/dbc.png" alt="DBC Logo" style="max-height: 20px; width: auto;">
					<label class="" style="color:#1E2F97;font-style:bold;font-size:20pt;" >DON BOSCO COLLEGE, INC.</label>
                    <button><i class="lni lni-search-alt"></i></button>
                  </form>
                </div>-->
              </div>
            </div>
            <div class="col-md-7 col-md-7 col-6">
              <div class="header-right"style="background-color:#EFBF04;" >
                <!-- notification start -->
                <div class="notification-box ml-15 d-none d-md-flex" style="background-color:#EFBF04;" > 
                  <button class="dropdown-toggle" type="button" id="notification" data-bs-toggle="dropdown"
                    aria-expanded="false">
                    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11 20.1667C9.88317 20.1667 8.88718 19.63 8.23901 18.7917H13.761C13.113 19.63 12.1169 20.1667 11 20.1667Z"
                        fill="" />
                      <path
                        d="M10.1157 2.74999C10.1157 2.24374 10.5117 1.83333 11 1.83333C11.4883 1.83333 11.8842 2.24374 11.8842 2.74999V2.82604C14.3932 3.26245 16.3051 5.52474 16.3051 8.24999V14.287C16.3051 14.5301 16.3982 14.7633 16.564 14.9352L18.2029 16.6342C18.4814 16.9229 18.2842 17.4167 17.8903 17.4167H4.10961C3.71574 17.4167 3.5185 16.9229 3.797 16.6342L5.43589 14.9352C5.6017 14.7633 5.69485 14.5301 5.69485 14.287V8.24999C5.69485 5.52474 7.60672 3.26245 10.1157 2.82604V2.74999Z"
                        fill="" />
                    </svg>
                    <span></span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notification">
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="../assets/images/lead/lead-6.png" alt="" />
                        </div>
                        <div class="content">
                          <h6>
                            John Doe
                            <span class="text-regular">
                              comment on a product.
                            </span>
                          </h6>
                          <p>
                            Lorem ipsum dolor sit amet, consect etur adipiscing
                            elit Vivamus tortor.
                          </p>
                          <span>10 mins ago</span>
                        </div>
                      </a>
                    </li>
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="../assets/images/lead/lead-6.jpg" alt="" />
                        </div>
                        <div class="content">
                          <h6>
                            Jonathon
                            <span class="text-regular">
                              like on a product.
                            </span>
                          </h6>
                          <p>
                            Lorem ipsum dolor sit amet, consect etur adipiscing
                            elit Vivamus tortor.
                          </p>
                          <span>10 mins ago</span>
                        </div>
                      </a>
                    </li>
                  </ul>
                </div>
                <!-- notification end -->
           
                <!-- profile start -->
                <div class="profile-box ml-15 d-flex align-items-center ">
                <button class="dropdown-toggle bg-transparent border-0" type="button" id="profile" data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="profile-info">
                    <div class="info">
                        <div class="image d-flex" style="width: 40px; height: 40px; overflow: hidden; border-radius: 50%;">
                        <img src="../assets/images/profile/uploads/<?php echo isset($_SESSION['profile_picture']) ? $_SESSION['profile_picture'] : 'profile-image.png'; ?>" alt="" style="width: 100%; height: 100%; object-fit: cover;" />
                        </div>
                        <div>
                          <h6 class="fw-500 dark-mode-text-override"><?php echo $_SESSION['name']; ?></h6>
                          <p class="dark-mode-text-override"><?php echo $_SESSION['role']; ?></p>
                        </div>
                      </div>
                    </div>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profile">
                  <li>
                    <div class="author-info flex items-center !p-1">
                        <div class="image" style="width: 40px; height: 40px; overflow: hidden; border-radius: 50%;">
                        <img src="../assets/images/profile/uploads/<?php echo isset($_SESSION['profile_picture']) ? $_SESSION['profile_picture'] : 'profile-image.png'; ?>" alt="image" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div class="content">
                        <h4 class="text-sm text-black dark:text-white"><?php echo isset($_SESSION['name']) ? $_SESSION['name'] : 'Guest'; ?></h4>
                        <p class="profile-dropdown-email"><?php echo isset($_SESSION['emp_email']) ? $_SESSION['emp_email'] : ''; ?></p>
                        </div>
                    </div>
                    </li>
                    <li class="divider"></li>
                    <li>
                      <a href="#" data-bs-toggle="modal" data-bs-target="#profileModal">
                        <i class="lni lni-user" style="color:#1E2F97"></i> View Profile
                      </a>
                    </li>
                     <li>
                        <a href="#" id="darkModeToggle">
                            <i class="lni lni-night" style="color:#1E2F97"></i> Dark Mode
                            <span class="dark-mode-status" style="margin-left: 5px; font-size: 0.8em; color: #718096;">(Off)</span>
                        </a>
                        </li>
                    <li>
                    <a href="../index.php"> <i class="lni lni-exit" style="color:#1E2F97"></i> Sign Out </a>
                    </li>
                  </ul>
                </div>
                <!-- profile end -->
              </div>
            </div>
          </div>
        </div>
      </header>
<!-- ========== header end ========== -->

<!-- Mobile Floating Dock -->
<div class="floating-dock">
  <a href="#" class="dock-item" id="dockMenu">
    <i class="lni lni-menu"></i>
    <span>Menu</span>
  </a>
  <a href="#" class="dock-item" id="dockChat">
    <i class="lni lni-comments"></i>
    <span>Chat</span>
  </a>
  <a href="notifications.php" class="dock-item position-relative">
    <i class="lni lni-alarm"></i>
    <?php //if ($unreadCount > 0): ?>
    <span class="position-absolute top-0 start-100 translate-middle notification-dot-mobile">
      <span class="visually-hidden">New alerts</span>
    </span>
    <?php //endif; ?>
    <span>Notifications</span>
  </a>
  <a href="#" class="dock-item" id="themeToggle">
    <i class="lni lni-sun"></i>
    <span>Theme</span>
  </a>
  <a href="#" class="dock-item" data-bs-toggle="modal" data-bs-target="#profileModal">
    <i class="lni lni-user"></i>
    <span>Profile</span>
  </a>
</div>

<!-- Profile Modal -->
<div class="modal fade profile-modal" id="profileModal" tabindex="-1" role="dialog" aria-labelledby="profileModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content shadow">
      <div class="modal-header border-bottom">
        <h5 class="modal-title fw-bold" id="profileModalLabel">My Profile</h5>
        <button type="button" class="btn-close bg-light" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body p-4">
        <div class="text-center mb-4">
          <div class="mx-auto mb-4" style="width: 150px; height: 150px;">
            <img src="../assets/images/profile/uploads/<?php echo isset($_SESSION['profile_picture']) ? $_SESSION['profile_picture'] : 'profile-image.png'; ?>" class="rounded-circle w-100 h-100" style="object-fit: cover;" id="currentProfilePic">
          </div>
          <span class="h4 text-dark dark:text-white" style="font-style: normal; font-weight: 600; display: block; margin-bottom: 15px;">
            <?php echo isset($_SESSION['name']) ? $_SESSION['name'] : 'Guest'; ?>
          </span>
          <button class="btn btn-secondary btn-sm rounded-pill" onclick="toggleEditMode()">
            <i class="lni lni-pencil me-1"></i> Edit Profile
          </button>
        </div>

        <div id="editProfileForm" style="display: none;">
          <!-- Profile Picture Form -->
          <form action="../scripts/userprofile.php" method="post" id="profilePictureForm" enctype="multipart/form-data" class="mt-3 mb-4">
            <div class="profile-upload-container" id="uploadContainer">
              <i class="lni lni-image"></i>
              <p class="upload-text">Drag & drop your profile picture here<br>or click to select</p>
              <p class="upload-hint">Supported formats: JPG, PNG, GIF (Max 5MB)</p>
              <input type="file" class="form-control d-none" id="profileImage" name="profileImage" accept="image/*">
            </div>
            <div class="invalid-feedback" id="fileError"></div>
            <div class="progress upload-progress" id="uploadProgress">
              <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <div class="text-end">
              <button type="submit" name="updatePicture" class="btn btn-secondary btn-sm rounded-pill" id="uploadButton" disabled>
                <i class="lni lni-upload me-1"></i> Update Picture
              </button>
            </div>
          </form>

          <hr class="my-4">
          
          <!-- Password Form -->
          <form action="../scripts/userprofile.php" method="post" id="passwordForm" class="mt-3">
            <div class="form-group mb-3">
              <label for="currentPassword">Current Password</label>
              <input type="password" class="form-control" id="currentPassword" name="currentPassword" placeholder="Enter current password">
            </div>
            <div class="form-group mb-3">
              <label for="newPassword">New Password</label>
              <input type="password" class="form-control" id="newPassword" name="newPassword" placeholder="Enter new password">
            </div>
            <div class="form-group mb-3">
              <label for="confirmPassword">Confirm New Password</label>
              <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" placeholder="Re-enter new password">
            </div>
            
            <div class="modal-footer border-top mt-4">
              <button type="button" class="btn btn-danger btn-sm rounded-pill" onclick="toggleEditMode()">
                <i class="lni lni-close me-1"></i> Cancel
              </button>
              <button type="submit" name="updatePassword" class="btn btn-secondary btn-sm rounded-pill">
                <i class="lni lni-lock me-1"></i> Update Password
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleEditMode() {
  const editForm = document.getElementById('editProfileForm');
  const isHidden = editForm.style.display === 'none';
  editForm.style.display = isHidden ? 'block' : 'none';
}

function validateFile(file) {
  const maxSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
  const fileError = document.getElementById('fileError');
  const uploadButton = document.getElementById('uploadButton');
  
  if (!file) {
    fileError.textContent = 'Please select a file.';
    fileError.style.display = 'block';
    uploadButton.disabled = true;
    return false;
  }
  
  if (!allowedTypes.includes(file.type)) {
    fileError.textContent = 'Please select a valid image file (JPG, PNG, or GIF).';
    fileError.style.display = 'block';
    uploadButton.disabled = true;
    return false;
  }
  
  if (file.size > maxSize) {
    fileError.textContent = 'File size must be less than 5MB.';
    fileError.style.display = 'block';
    uploadButton.disabled = true;
    return false;
  }
  
  fileError.style.display = 'none';
  uploadButton.disabled = false;
  return true;
}

function resizeImage(img, maxSize) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  let width = img.width;
  let height = img.height;
  
  if (width > height) {
    if (width > maxSize) {
      height = height * (maxSize / width);
      width = maxSize;
    }
  } else {
    if (height > maxSize) {
      width = width * (maxSize / height);
      height = maxSize;
    }
  }
  
  canvas.width = width;
  canvas.height = height;
  ctx.drawImage(img, 0, 0, width, height);
  
  return canvas.toDataURL('image/jpeg', 0.9);
}

// Handle drag and drop
const uploadContainer = document.getElementById('uploadContainer');
const fileInput = document.getElementById('profileImage');

['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
  uploadContainer.addEventListener(eventName, (e) => {
    e.preventDefault();
    e.stopPropagation();
  });
});

['dragenter', 'dragover'].forEach(eventName => {
  uploadContainer.addEventListener(eventName, () => {
    uploadContainer.classList.add('dragover');
  });
});

['dragleave', 'drop'].forEach(eventName => {
  uploadContainer.addEventListener(eventName, () => {
    uploadContainer.classList.remove('dragover');
  });
});

uploadContainer.addEventListener('drop', (e) => {
  const files = e.dataTransfer.files;
  if (files.length) {
    fileInput.files = files;
    handleFileSelect(files[0]);
  }
});

uploadContainer.addEventListener('click', () => fileInput.click());

// Handle file selection
fileInput.addEventListener('change', (e) => {
  const file = e.target.files[0];
  if (file) {
    handleFileSelect(file);
  }
});

function handleFileSelect(file) {
  if (!validateFile(file)) return;
  
  const reader = new FileReader();
  reader.onload = function(e) {
    const img = new Image();
    img.onload = function() {
      const resizedImage = resizeImage(img, 500);
      document.getElementById('currentProfilePic').src = resizedImage;
      
      // Show upload progress bar simulation
      const progressBar = document.querySelector('.upload-progress');
      const progressBarInner = progressBar.querySelector('.progress-bar');
      progressBar.style.display = 'block';
      let progress = 0;
      const interval = setInterval(() => {
        progress += 5;
        progressBarInner.style.width = `${progress}%`;
        if (progress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            progressBar.style.display = 'none';
            progressBarInner.style.width = '0%';
          }, 500);
        }
      }, 50);
    };
    img.src = e.target.result;
  };
  reader.readAsDataURL(file);
}

// Form submission for profile picture
document.getElementById('profilePictureForm').addEventListener('submit', async (e) => {
  e.preventDefault();
  const fileInput = document.getElementById('profileImage');
  const fileError = document.getElementById('fileError');
  const uploadButton = document.getElementById('uploadButton');
  const currentProfilePic = document.getElementById('currentProfilePic');
  const profileDropdownPic = document.querySelector('.profile-info .image img');
  const authorInfoPic = document.querySelector('.author-info .image img');

  if (!fileInput.files.length) {
    fileError.textContent = 'Please select a file to upload.';
    fileError.style.display = 'block';
    return;
  }

  if (!validateFile(fileInput.files[0])) {
    return;
  }

  uploadButton.disabled = true;
  uploadButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Uploading...';

  const formData = new FormData();
  formData.append('profileImage', fileInput.files[0]);
  formData.append('updatePicture', 'true');

  try {
    const response = await fetch('/cdas/scripts/userprofile.php', {
      method: 'POST',
      body: formData
    });
    const data = await response.json();

    if (data.success) {
      const newImageUrl = `../assets/images/profile/uploads/${data.fileNameNew}`;
      currentProfilePic.src = newImageUrl;
      profileDropdownPic.src = newImageUrl;
      authorInfoPic.src = newImageUrl;

      alert(data.message);
    } else {
      fileError.textContent = data.message;
      fileError.style.display = 'block';
    }
  } catch (error) {
    console.error('Error:', error);
    fileError.textContent = 'An unexpected error occurred.';
    fileError.style.display = 'block';
  } finally {
    uploadButton.disabled = false;
    uploadButton.innerHTML = '<i class="lni lni-upload me-1"></i> Update Picture';
    fileInput.value = '';
  }
});

// Form submission for password
document.getElementById('passwordForm').addEventListener('submit', async (e) => {
  e.preventDefault();
  const currentPassword = document.getElementById('currentPassword').value;
  const newPassword = document.getElementById('newPassword').value;
  const confirmPassword = document.getElementById('confirmPassword').value;
  const passwordForm = document.getElementById('passwordForm');

  if (newPassword !== confirmPassword) {
    alert('New password and confirm password do not match.');
    return;
  }

  if (newPassword === '') {
    alert('New password cannot be empty.');
    return;
  }

  const formData = new FormData();
  formData.append('currentPassword', currentPassword);
  formData.append('newPassword', newPassword);
  formData.append('confirmPassword', confirmPassword);
  formData.append('updatePassword', 'true');

  try {
    const response = await fetch('/cdas/scripts/userprofile.php', {
      method: 'POST',
      body: formData
    });
    const data = await response.json();

    if (data.success) {
      alert(data.message);
      passwordForm.reset(); 
      toggleEditMode(); 
    } else {
      alert(data.message);
    }
  } catch (error) {
    console.error('Error:', error);
    alert('An unexpected error occurred during password update.');
  }
});
</script>
