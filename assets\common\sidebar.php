<?php
	session_start();
	require_once('../core/dbcon.ini');
	include('../includes/session.php');
	include('../query/access.qry');
	include('../query/menu.qry');

	$access = new ACCESS;
	$menufnc = new MENU();

	$allmenu=$menufnc->get_allmenu($db1);

?>

<aside class="sidebar-nav-wrapper" style="background-color:#1E2F97;">
    <div class="navbar-logo">
        <a href="index.php" class="" >
            <h4 class="text-left mb-0" style="color:#FFC000;font-style:bold;font-size:18pt;">CDAS</h4>
            <h4 class="text-left mb-0" style="color:#FFC000;font-style:bold;font-size:10pt;">COLLEGE DEPARTMENT ATTENDANCE SYSTEM</h4>
        </a>
    </div>
    <nav class="sidebar-nav">
        <ul>
             <li class="nav-item nav-item-has-children">
                <a href="index.php">
                    <span class="icon" style="color:#FFC000;">
                        <i class="lni lni-dashboard"></i>
                    </span>
                    <span class="text" style="color:#FFFFFF;font-style:bold;font-size:10pt;">Dashboard</span>
                </a>
            </li>
			<?php
			for($i=0;$i<=count($allmenu)-1;$i++)
			{
				//echo $allmenu[$i]['menuid'];
				$userpassportmenuid=$access->get_userpassportmenuid($db1,$allmenu[$i]['menuid'],$_SESSION['uaid']);
				
				if(count($userpassportmenuid)>=1)
				{
						
				?>
			
					<li class="nav-item nav-item-has-children">
						<a
							href="#0"
							class="collapsed"
							data-bs-toggle="collapse"
							data-bs-target="#ddmenu_<?php echo $allmenu[$i]['menuid']; ?>"
							aria-controls="ddmenu_<?php echo $allmenu[$i]['menuid']; ?>"
							aria-expanded="false"
							aria-label="Toggle navigation"
						>
						
							<span class="icon" style="color:#FFC000;">
								<i class="<?php echo $allmenu[$i]['icon'] ?>"></i>
							</span>
						<span class="text" style="color:#FFFFFF;font-style:bold;font-size:10pt;"><?php echo $allmenu[$i]['menutitle']; ?></span>
                        <span class="caret" style="color:#FFFFFF;float:right;"><i class="lni lni-angle-double-down"></i></span>
                        </a>
						<ul id="ddmenu_<?php echo $allmenu[$i]['menuid']; ?>" class="collapse dropdown-nav">
						<?php
						for($a=0;$a<=count($userpassportmenuid)-1;$a++)
						{
							if ($userpassportmenuid[$a]['submenuid'] != 0 && $userpassportmenuid[$a]['submenuid'] !== null) {
								$submenumenuid=$menufnc->get_submenumenuid($db1,$userpassportmenuid[$a]['submenuid']);
					
								if ($submenumenuid) {
									echo '<li>';
									echo '<a href="'.$submenumenuid['submenulink'].'" style="color:#FFC000;" class="">';
									echo '<i class="'.$submenumenuid['icon'].'"></i>';
									echo $submenumenuid['submenutitle'];
									echo '</a>';
									echo '</li>';

                                    /*	for($a=0;$a<=count($userpassportmenuid)-1;$a++)
                                        {
                                            $submenumenuid=$menufnc->get_submenumenuid($db1,$userpassportmenuid[$a]['submenuid']);
                                
                                            echo '<li>';
                                            echo '<a href="'.$submenumenuid['submenulink'].'" style="color:#FFC000;" class="">';
                                            echo '<i class="'.$submenumenuid['icon'].'"></i>';
                                            echo $submenumenuid['submenutitle'];
                                            echo '</a>';
                                            echo '</li>';
                                        } */
                                    
								}
							}
						} 
						?>
						</ul>
					</li>
				<?php
				
				}
			}
			
			?>
			
		</ul>
	</nav>
</aside>
	
	

<!-- ======== sidebar-nav start =========== --
<aside class="sidebar-nav-wrapper" style="background-color:#1E2F97;">
    <div class="navbar-logo">
        <a href="index.php" class="" >
            <h4 class="text-left mb-0" style="color:#FFC000;font-style:bold;font-size:18pt;">CDAS</h4>
            <h4 class="text-left mb-0" style="color:#FFC000;font-style:bold;font-size:10pt;">COLLEGE DEPARTMENT ATTENDANCE SYSTEM</h4>
        </a>
    </div>
    <nav class="sidebar-nav">
        <ul>
            <li class="nav-item">
                <a href="index.php">
                    <span class="icon" style="color:#FFC000;">
                        <i class="lni lni-dashboard"></i>
                    </span>
                    <span class="text" style="color:#FFFFFF;font-style:bold;font-size:10pt;">Dashboard</span>
                </a>
            </li>
            <?php
			/*
            if(!empty($menus)) {
                for($i = 0; $i < count($menus); $i++) {
                    $submenus = $menu->get_submenu($db1, $menus[$i]['menuid'], $userid);
                    $hasSubmenus = count($submenus) > 0;
                    $menuIcon = !empty($menus[$i]['icon']) ? $menus[$i]['icon'] : 'lni lni-grid-alt';
                    
                    echo '<li class="nav-item '.($hasSubmenus ? 'nav-item-has-children' : '').'">';
                    echo '<a href="'.($hasSubmenus ? '#0' : (isset($menus[$i]['link']) ? $menus[$i]['link'] : '#')).'"';
                   
                   if($hasSubmenus) {
                       echo ' class="collapsed" data-bs-toggle="collapse" data-bs-target="#ddmenu_'.$menus[$i]['menuid'].'"';
                       echo ' aria-controls="ddmenu_'.$menus[$i]['menuid'].'" aria-expanded="false" aria-label="Toggle navigation"';
                   }
                   
                   echo '>';
                   echo '<span class="icon" style="color:#FFC000;">';
                   echo '<i class="'.$menuIcon.'"></i>';
                   echo '</span>';
                   echo '<span class="text" style="color:#FFFFFF;font-style:bold;font-size:10pt;">'.$menus[$i]['menutitle'].'</span>';
                   if($hasSubmenus) {
                       echo '<i class="mdi mdi-chevron-down" style="color:#FFC000;"></i>';
                   }
                   echo '</a>';
                   
                   if($hasSubmenus) {
                       echo '<ul id="ddmenu_'.$menus[$i]['menuid'].'" class="collapse dropdown-nav">';
                        
                        for($j = 0; $j < count($submenus); $j++) {
                            $submenuIcon = !empty($submenus[$j]['icon']) ? $submenus[$j]['icon'] : 'lni lni-angle-right';
                            
                            echo '<li>';
                            echo '<a href="'.$submenus[$j]['link'].'" style="color:#FFC000;">';
                            echo '<i class="'.$submenuIcon.'"></i>';
                            echo $submenus[$j]['submenutitle'];
                            echo '</a>';
                            echo '</li>';
                        }
                        
                        echo '</ul>';
                    }
                    
                    echo '</li>';
                }
            }*/
            ?>
        </ul>
    </nav>
</aside>-->
<div class="overlay"></div>
<!-- ======== sidebar-nav end =========== -->

<style>
.caret i {
    transition: transform 0.3s ease;
}
.nav-item-has-children > a[aria-expanded="true"] .caret i {
    transform: rotate(180deg);
}
</style>
