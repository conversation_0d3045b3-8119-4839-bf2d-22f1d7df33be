<?php
class STUDENTSFNC
{
  
    function update_student_image($db, $student_no, $image)
    {
        $stmt = $db->prepare("UPDATE students SET image = ? WHERE student_no = ?");
        return $stmt->execute(array($image, $student_no));
    }

    function delete_student($db, $student_no)
    {
        $stmt = $db->prepare("DELETE FROM students WHERE student_no = ?");
        return $stmt->execute(array($student_no));
    }
	
	///////////////////////YZAI//////////
	
	function get_allcourses($db) 
	{
        $data = array();
        $stmt1 = $db->prepare("SELECT * FROM cdas_courses
		ORDER BY gy_id ASC");
        $stmt1->execute();
        for($i=1; $i<=$stmt1->rowCount(); $i++)
            $data[] = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $data;
    }
	function get_allactivity($db) 
	{
        $data = array();
        $stmt1 = $db->prepare("SELECT * FROM cdas_activity
		ORDER BY activity_id ASC");
        $stmt1->execute();
        for($i=1; $i<=$stmt1->rowCount(); $i++)
            $data[] = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function get_allstudents($db) 
	{
        $data = array();
        $stmt1 = $db->prepare("SELECT * FROM cdas_student");
        $stmt1->execute();
        for($i=1; $i<=$stmt1->rowCount(); $i++)
            $data[] = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $data;
    }
	 function get_allstudentss($db) 
	{
        $data = array();
        $stmt1 = $db->prepare("SELECT * FROM cdas_student");
        $stmt1->execute();
        for($i=1; $i<=$stmt1->rowCount(); $i++)
            $data[] = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $data;
    }
	
}
?>
