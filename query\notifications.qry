<?php

class NOTIFICATIONS
{
    function create_login_notification($db, $staff_id, $firstname, $lastname)
    {
        $message = "New login: Staff $firstname $lastname has signed in";
        $stmt = $db->prepare("INSERT INTO notifications (message, is_shown, created_at) VALUES (?, 0, NOW())");
        $stmt->execute(array($message));
    }

    function get_login_notifications($db)
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM notifications WHERE message LIKE 'New login: Staff %' ORDER BY created_at DESC");
        $stmt->execute();
        for ($i = 1; $i <= $stmt->rowCount(); $i++) {
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        return $data;
    }

    function get_unread_login_count($db)
    {
        $stmt = $db->prepare("SELECT COUNT(*) FROM notifications WHERE message LIKE 'New login: Staff %' AND is_shown = 0");
        $stmt->execute();
        return $stmt->fetchColumn();
    }

    function mark_as_read($db, $notification_ids)
    {
        if (!is_array($notification_ids) || empty($notification_ids)) {
            return;
        }

        $placeholders = implode(',', array_fill(0, count($notification_ids), '?'));
        $stmt = $db->prepare("UPDATE notifications SET is_shown = 1, read_at = NOW() WHERE id IN ($placeholders)");
        $stmt->execute($notification_ids);
    }

    function delete_notifications($db, $notification_ids)
    {
        if (!is_array($notification_ids) || empty($notification_ids)) {
            return;
        }
        $placeholders = implode(',', array_fill(0, count($notification_ids), '?'));
        $stmt = $db->prepare("DELETE FROM notifications WHERE id IN ($placeholders)");
        $stmt->execute($notification_ids);
    }

    function get_notification_by_id($db, $notification_id) 
    {
        $stmt = $db->prepare("SELECT * FROM notifications WHERE id = ?");
        $stmt->execute([$notification_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
