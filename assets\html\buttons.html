<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="shortcut icon" href="assets/images/favicon.svg" type="image/x-icon" />
    <title>Button | PlainAdmin Demo</title>

    <!-- ========== All CSS files linkup ========= -->
    <link rel="stylesheet" href="assets/css/bootstrap.min.css" />
    <link rel="stylesheet" href="assets/css/lineicons.css" />
    <link rel="stylesheet" href="assets/css/materialdesignicons.min.css" />
    <link rel="stylesheet" href="assets/css/fullcalendar.css" />
    <link rel="stylesheet" href="assets/css/main.css" />
  </head>
  <body>
    <!-- ======== Preloader =========== -->
    <div id="preloader">
      <div class="spinner"></div>
    </div>
    <!-- ======== Preloader =========== -->

    <!-- ======== sidebar-nav start =========== -->
    <aside class="sidebar-nav-wrapper">
      <div class="navbar-logo">
        <a href="index.html">
          <img src="assets/images/logo/logo.svg" alt="logo" />
        </a>
      </div>
      <nav class="sidebar-nav">
        <ul>
          <li class="nav-item nav-item-has-children">
            <a
              href="#0"
              class="collapsed"
              data-bs-toggle="collapse"
              data-bs-target="#ddmenu_1"
              aria-controls="ddmenu_1"
              aria-expanded="false"
              aria-label="Toggle navigation"
            >
              <span class="icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M8.74999 18.3333C12.2376 18.3333 15.1364 15.8128 15.7244 12.4941C15.8448 11.8143 15.2737 11.25 14.5833 11.25H9.99999C9.30966 11.25 8.74999 10.6903 8.74999 10V5.41666C8.74999 4.7263 8.18563 4.15512 7.50586 4.27556C4.18711 4.86357 1.66666 7.76243 1.66666 11.25C1.66666 15.162 4.83797 18.3333 8.74999 18.3333Z" />
                  <path
                    d="M17.0833 10C17.7737 10 18.3432 9.43708 18.2408 8.75433C17.7005 5.14918 14.8508 2.29947 11.2457 1.75912C10.5629 1.6568 10 2.2263 10 2.91665V9.16666C10 9.62691 10.3731 10 10.8333 10H17.0833Z" />
                </svg>
              </span>
              <span class="text">Dashboard</span>
            </a>
            <ul id="ddmenu_1" class="collapse dropdown-nav">
              <li>
                <a href="index.html"> eCommerce </a>
              </li>
            </ul>
          </li>
          <li class="nav-item nav-item-has-children">
            <a
              href="#0"
              class="collapsed"
              data-bs-toggle="collapse"
              data-bs-target="#ddmenu_2"
              aria-controls="ddmenu_2"
              aria-expanded="false"
              aria-label="Toggle navigation"
            >
              <span class="icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M11.8097 1.66667C11.8315 1.66667 11.8533 1.6671 11.875 1.66796V4.16667C11.875 5.43232 12.901 6.45834 14.1667 6.45834H16.6654C16.6663 6.48007 16.6667 6.50186 16.6667 6.5237V16.6667C16.6667 17.5872 15.9205 18.3333 15 18.3333H5.00001C4.07954 18.3333 3.33334 17.5872 3.33334 16.6667V3.33334C3.33334 2.41286 4.07954 1.66667 5.00001 1.66667H11.8097ZM6.66668 7.70834C6.3215 7.70834 6.04168 7.98816 6.04168 8.33334C6.04168 8.67851 6.3215 8.95834 6.66668 8.95834H10C10.3452 8.95834 10.625 8.67851 10.625 8.33334C10.625 7.98816 10.3452 7.70834 10 7.70834H6.66668ZM6.04168 11.6667C6.04168 12.0118 6.3215 12.2917 6.66668 12.2917H13.3333C13.6785 12.2917 13.9583 12.0118 13.9583 11.6667C13.9583 11.3215 13.6785 11.0417 13.3333 11.0417H6.66668C6.3215 11.0417 6.04168 11.3215 6.04168 11.6667ZM6.66668 14.375C6.3215 14.375 6.04168 14.6548 6.04168 15C6.04168 15.3452 6.3215 15.625 6.66668 15.625H13.3333C13.6785 15.625 13.9583 15.3452 13.9583 15C13.9583 14.6548 13.6785 14.375 13.3333 14.375H6.66668Z" />
                  <path
                    d="M13.125 2.29167L16.0417 5.20834H14.1667C13.5913 5.20834 13.125 4.74197 13.125 4.16667V2.29167Z" />
                </svg>
              </span>
              <span class="text">Pages</span>
            </a>
            <ul id="ddmenu_2" class="collapse dropdown-nav">
              <li>
                <a href="settings.html"> Settings </a>
              </li>
              <li>
                <a href="blank-page.html"> Blank Page </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="inv'">
              <span class="icon">
                <svg
                  width="22"
                  height="22"
                  viewBox="0 0 22 22"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M17.4166 7.33333C18.9383 7.33333 20.1666 8.56167 20.1666 10.0833V15.5833H16.4999V19.25H5.49992V15.5833H1.83325V10.0833C1.83325 8.56167 3.06159 7.33333 4.58325 7.33333H5.49992V2.75H16.4999V7.33333H17.4166ZM7.33325 4.58333V7.33333H14.6666V4.58333H7.33325ZM14.6666 17.4167V13.75H7.33325V17.4167H14.6666ZM16.4999 13.75H18.3333V10.0833C18.3333 9.57917 17.9208 9.16667 17.4166 9.16667H4.58325C4.07909 9.16667 3.66659 9.57917 3.66659 10.0833V13.75H5.49992V11.9167H16.4999V13.75ZM17.4166 10.5417C17.4166 11.0458 17.0041 11.4583 16.4999 11.4583C15.9958 11.4583 15.5833 11.0458 15.5833 10.5417C15.5833 10.0375 15.9958 9.625 16.4999 9.625C17.0041 9.625 17.4166 10.0375 17.4166 10.5417Z"
                  />
                </svg>
              </span>
              <span class="text">Invoice</span>
            </a>
          </li>
          <li class="nav-item nav-item-has-children">
            <a
              href="#0"
              class="collapsed"
              data-bs-toggle="collapse"
              data-bs-target="#ddmenu_3"
              aria-controls="ddmenu_3"
              aria-expanded="false"
              aria-label="Toggle navigation"
            >
              <span class="icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M14.9211 10.1294C15.1652 9.88534 15.1652 9.48967 14.9211 9.24559L10.7544 5.0789C10.5103 4.83482 10.1147 4.83482 9.87057 5.0789C9.62649 5.32297 9.62649 5.71871 9.87057 5.96278L12.9702 9.06251H1.97916C1.63398 9.06251 1.35416 9.34234 1.35416 9.68751C1.35416 10.0327 1.63398 10.3125 1.97916 10.3125H12.9702L9.87057 13.4123C9.62649 13.6563 9.62649 14.052 9.87057 14.2961C10.1147 14.5402 10.5103 14.5402 10.7544 14.2961L14.9211 10.1294Z" />
                  <path
                    d="M11.6383 15.18L15.805 11.0133C16.5373 10.2811 16.5373 9.09391 15.805 8.36166L11.6383 4.195C11.2722 3.82888 10.7923 3.64582 10.3125 3.64582V3.02082C10.3125 2.10035 11.0587 1.35416 11.9792 1.35416H16.9792C17.8997 1.35416 18.6458 2.10035 18.6458 3.02082V16.3542C18.6458 17.2747 17.8997 18.0208 16.9792 18.0208H11.9792C11.0587 18.0208 10.3125 17.2747 10.3125 16.3542V15.7292C10.7923 15.7292 11.2722 15.5461 11.6383 15.18Z" />
                </svg>
              </span>
              <span class="text">Auth</span>
            </a>
            <ul id="ddmenu_3" class="collapse dropdown-nav">
              <li>
                <a href="signin.html"> Sign In </a>
              </li>
              <li>
                <a href="signup.html"> Sign Up </a>
              </li>
            </ul>
          </li>
          <span class="divider"><hr /></span>
          <li class="nav-item nav-item-has-children">
            <a
              href="#0"
              class=""
              data-bs-toggle="collapse"
              data-bs-target="#ddmenu_4"
              aria-controls="ddmenu_4"
              aria-expanded="true"
              aria-label="Toggle navigation"
            >
              <span class="icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M1.66666 5.41669C1.66666 3.34562 3.34559 1.66669 5.41666 1.66669C7.48772 1.66669 9.16666 3.34562 9.16666 5.41669C9.16666 7.48775 7.48772 9.16669 5.41666 9.16669C3.34559 9.16669 1.66666 7.48775 1.66666 5.41669Z" />
                  <path
                    d="M1.66666 14.5834C1.66666 12.5123 3.34559 10.8334 5.41666 10.8334C7.48772 10.8334 9.16666 12.5123 9.16666 14.5834C9.16666 16.6545 7.48772 18.3334 5.41666 18.3334C3.34559 18.3334 1.66666 16.6545 1.66666 14.5834Z" />
                  <path
                    d="M10.8333 5.41669C10.8333 3.34562 12.5123 1.66669 14.5833 1.66669C16.6544 1.66669 18.3333 3.34562 18.3333 5.41669C18.3333 7.48775 16.6544 9.16669 14.5833 9.16669C12.5123 9.16669 10.8333 7.48775 10.8333 5.41669Z" />
                  <path
                    d="M10.8333 14.5834C10.8333 12.5123 12.5123 10.8334 14.5833 10.8334C16.6544 10.8334 18.3333 12.5123 18.3333 14.5834C18.3333 16.6545 16.6544 18.3334 14.5833 18.3334C12.5123 18.3334 10.8333 16.6545 10.8333 14.5834Z" />
                </svg>
              </span>
              <span class="text">UI Elements </span>
            </a>
            <ul id="ddmenu_4" class="collapsed show dropdown-nav">
              <li>
                <a href="alerts.html"> Alerts </a>
              </li>
              <li>
                <a href="buttons.html" class="active"> Buttons </a>
              </li>
              <li>
                <a href="cards.html"> Cards </a>
              </li>
              <li>
                <a href="typography.html"> Typography </a>
              </li>
            </ul>
          </li>
          <li class="nav-item nav-item-has-children">
            <a
              href="#0"
              class="collapsed"
              data-bs-toggle="collapse"
              data-bs-target="#ddmenu_55"
              aria-controls="ddmenu_55"
              aria-expanded="false"
              aria-label="Toggle navigation"
            >
              <span class="icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M5.48663 1.1466C5.77383 0.955131 6.16188 1.03274 6.35335 1.31994L6.87852 2.10769C7.20508 2.59755 7.20508 3.23571 6.87852 3.72556L6.35335 4.51331C6.16188 4.80052 5.77383 4.87813 5.48663 4.68666C5.19943 4.49519 5.12182 4.10715 5.31328 3.81994L5.83845 3.03219C5.88511 2.96221 5.88511 2.87105 5.83845 2.80106L5.31328 2.01331C5.12182 1.72611 5.19943 1.33806 5.48663 1.1466Z" />
                  <path
                    d="M2.49999 5.83331C2.03976 5.83331 1.66666 6.2064 1.66666 6.66665V10.8333C1.66666 13.5948 3.90523 15.8333 6.66666 15.8333H9.99999C12.1856 15.8333 14.0436 14.431 14.7235 12.4772C14.8134 12.4922 14.9058 12.5 15 12.5H16.6667C17.5872 12.5 18.3333 11.7538 18.3333 10.8333V8.33331C18.3333 7.41284 17.5872 6.66665 16.6667 6.66665H15C15 6.2064 14.6269 5.83331 14.1667 5.83331H2.49999ZM14.9829 11.2496C14.9942 11.1123 15 10.9735 15 10.8333V7.91665H16.6667C16.8967 7.91665 17.0833 8.10319 17.0833 8.33331V10.8333C17.0833 11.0634 16.8967 11.25 16.6667 11.25H15L14.9898 11.2498L14.9829 11.2496Z" />
                  <path
                    d="M8.85332 1.31994C8.6619 1.03274 8.27383 0.955131 7.98663 1.1466C7.69943 1.33806 7.62182 1.72611 7.81328 2.01331L8.33848 2.80106C8.38507 2.87105 8.38507 2.96221 8.33848 3.03219L7.81328 3.81994C7.62182 4.10715 7.69943 4.49519 7.98663 4.68666C8.27383 4.87813 8.6619 4.80052 8.85332 4.51331L9.37848 3.72556C9.70507 3.23571 9.70507 2.59755 9.37848 2.10769L8.85332 1.31994Z" />
                  <path
                    d="M10.4867 1.1466C10.7738 0.955131 11.1619 1.03274 11.3533 1.31994L11.8785 2.10769C12.2051 2.59755 12.2051 3.23571 11.8785 3.72556L11.3533 4.51331C11.1619 4.80052 10.7738 4.87813 10.4867 4.68666C10.1994 4.49519 10.1218 4.10715 10.3133 3.81994L10.8385 3.03219C10.8851 2.96221 10.8851 2.87105 10.8385 2.80106L10.3133 2.01331C10.1218 1.72611 10.1994 1.33806 10.4867 1.1466Z" />
                  <path
                    d="M2.49999 16.6667C2.03976 16.6667 1.66666 17.0398 1.66666 17.5C1.66666 17.9602 2.03976 18.3334 2.49999 18.3334H14.1667C14.6269 18.3334 15 17.9602 15 17.5C15 17.0398 14.6269 16.6667 14.1667 16.6667H2.49999Z" />
                </svg>
              </span>
              <span class="text">Icons</span>
            </a>
            <ul id="ddmenu_55" class="collapse dropdown-nav">
              <li>
                <a href="icons.html"> LineIcons </a>
              </li>
              <li>
                <a href="mdi-icons.html"> MDI Icons </a>
              </li>
            </ul>
          </li>
          <li class="nav-item nav-item-has-children">
            <a
              href="#0"
              class="collapsed"
              data-bs-toggle="collapse"
              data-bs-target="#ddmenu_5"
              aria-controls="ddmenu_5"
              aria-expanded="false"
              aria-label="Toggle navigation"
            >
              <span class="icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M4.16666 3.33335C4.16666 2.41288 4.91285 1.66669 5.83332 1.66669H14.1667C15.0872 1.66669 15.8333 2.41288 15.8333 3.33335V16.6667C15.8333 17.5872 15.0872 18.3334 14.1667 18.3334H5.83332C4.91285 18.3334 4.16666 17.5872 4.16666 16.6667V3.33335ZM6.04166 5.00002C6.04166 5.3452 6.32148 5.62502 6.66666 5.62502H13.3333C13.6785 5.62502 13.9583 5.3452 13.9583 5.00002C13.9583 4.65485 13.6785 4.37502 13.3333 4.37502H6.66666C6.32148 4.37502 6.04166 4.65485 6.04166 5.00002ZM6.66666 6.87502C6.32148 6.87502 6.04166 7.15485 6.04166 7.50002C6.04166 7.8452 6.32148 8.12502 6.66666 8.12502H13.3333C13.6785 8.12502 13.9583 7.8452 13.9583 7.50002C13.9583 7.15485 13.6785 6.87502 13.3333 6.87502H6.66666ZM6.04166 10C6.04166 10.3452 6.32148 10.625 6.66666 10.625H9.99999C10.3452 10.625 10.625 10.3452 10.625 10C10.625 9.65485 10.3452 9.37502 9.99999 9.37502H6.66666C6.32148 9.37502 6.04166 9.65485 6.04166 10ZM9.99999 16.6667C10.9205 16.6667 11.6667 15.9205 11.6667 15C11.6667 14.0795 10.9205 13.3334 9.99999 13.3334C9.07949 13.3334 8.33332 14.0795 8.33332 15C8.33332 15.9205 9.07949 16.6667 9.99999 16.6667Z" />
                </svg>
              </span>
              <span class="text"> Forms </span>
            </a>
            <ul id="ddmenu_5" class="collapse dropdown-nav">
              <li>
                <a href="form-elements.html"> Form Elements </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="tables.html">
              <span class="icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M1.66666 4.16667C1.66666 3.24619 2.41285 2.5 3.33332 2.5H16.6667C17.5872 2.5 18.3333 3.24619 18.3333 4.16667V9.16667C18.3333 10.0872 17.5872 10.8333 16.6667 10.8333H3.33332C2.41285 10.8333 1.66666 10.0872 1.66666 9.16667V4.16667Z" />
                  <path
                    d="M1.875 13.75C1.875 13.4048 2.15483 13.125 2.5 13.125H17.5C17.8452 13.125 18.125 13.4048 18.125 13.75C18.125 14.0952 17.8452 14.375 17.5 14.375H2.5C2.15483 14.375 1.875 14.0952 1.875 13.75Z" />
                  <path
                    d="M2.5 16.875C2.15483 16.875 1.875 17.1548 1.875 17.5C1.875 17.8452 2.15483 18.125 2.5 18.125H17.5C17.8452 18.125 18.125 17.8452 18.125 17.5C18.125 17.1548 17.8452 16.875 17.5 16.875H2.5Z" />
                </svg>
              </span>
              <span class="text">Tables</span>
            </a>
          </li>
          <span class="divider"><hr /></span>

          <li class="nav-item">
            <a href="notification.html">
              <span class="icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M10.8333 2.50008C10.8333 2.03984 10.4602 1.66675 9.99999 1.66675C9.53975 1.66675 9.16666 2.03984 9.16666 2.50008C9.16666 2.96032 9.53975 3.33341 9.99999 3.33341C10.4602 3.33341 10.8333 2.96032 10.8333 2.50008Z" />
                  <path
                    d="M17.5 5.41673C17.5 7.02756 16.1942 8.33339 14.5833 8.33339C12.9725 8.33339 11.6667 7.02756 11.6667 5.41673C11.6667 3.80589 12.9725 2.50006 14.5833 2.50006C16.1942 2.50006 17.5 3.80589 17.5 5.41673Z" />
                  <path
                    d="M11.4272 2.69637C10.9734 2.56848 10.4947 2.50006 10 2.50006C7.10054 2.50006 4.75003 4.85057 4.75003 7.75006V9.20873C4.75003 9.72814 4.62082 10.2393 4.37404 10.6963L3.36705 12.5611C2.89938 13.4272 3.26806 14.5081 4.16749 14.9078C7.88074 16.5581 12.1193 16.5581 15.8326 14.9078C16.732 14.5081 17.1007 13.4272 16.633 12.5611L15.626 10.6963C15.43 10.3333 15.3081 9.93606 15.2663 9.52773C15.0441 9.56431 14.8159 9.58339 14.5833 9.58339C12.2822 9.58339 10.4167 7.71791 10.4167 5.41673C10.4167 4.37705 10.7975 3.42631 11.4272 2.69637Z" />
                  <path
                    d="M7.48901 17.1925C8.10004 17.8918 8.99841 18.3335 10 18.3335C11.0016 18.3335 11.9 17.8918 12.511 17.1925C10.8482 17.4634 9.15183 17.4634 7.48901 17.1925Z" />
                </svg>
              </span>
              <span class="text">Notifications</span>
            </a>
          </li>
        </ul>
      </nav>
      <div class="promo-box">
        <div class="promo-icon">
          <img class="mx-auto" src="./assets/images/logo/logo-icon-big.svg" alt="Logo">
        </div>
        <h3>Upgrade to PRO</h3>
        <p>Improve your development process and start doing more with PlainAdmin PRO!</p>
        <a href="https://plainadmin.com/pro" target="_blank" rel="nofollow" class="main-btn primary-btn btn-hover">
          Upgrade to PRO
        </a>
      </div>
    </aside>
    <div class="overlay"></div>
    <!-- ======== sidebar-nav end =========== -->

    <!-- ======== main-wrapper start =========== -->
    <main class="main-wrapper">
      <!-- ========== header start ========== -->
      <header class="header">
        <div class="container-fluid">
          <div class="row">
            <div class="col-lg-5 col-md-5 col-6">
              <div class="header-left d-flex align-items-center">
                <div class="menu-toggle-btn mr-15">
                  <button id="menu-toggle" class="main-btn primary-btn btn-hover">
                    <i class="lni lni-chevron-left me-2"></i> Menu
                  </button>
                </div>
                <div class="header-search d-none d-md-flex">
                  <form action="#">
                    <input type="text" placeholder="Search..." />
                    <button><i class="lni lni-search-alt"></i></button>
                  </form>
                </div>
              </div>
            </div>
            <div class="col-lg-7 col-md-7 col-6">
              <div class="header-right">
                <!-- notification start -->
                <div class="notification-box ml-15 d-none d-md-flex">
                  <button class="dropdown-toggle" type="button" id="notification" data-bs-toggle="dropdown"
                    aria-expanded="false">
                    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11 20.1667C9.88317 20.1667 8.88718 19.63 8.23901 18.7917H13.761C13.113 19.63 12.1169 20.1667 11 20.1667Z"
                        fill="" />
                      <path
                        d="M10.1157 2.74999C10.1157 2.24374 10.5117 1.83333 11 1.83333C11.4883 1.83333 11.8842 2.24374 11.8842 2.74999V2.82604C14.3932 3.26245 16.3051 5.52474 16.3051 8.24999V14.287C16.3051 14.5301 16.3982 14.7633 16.564 14.9352L18.2029 16.6342C18.4814 16.9229 18.2842 17.4167 17.8903 17.4167H4.10961C3.71574 17.4167 3.5185 16.9229 3.797 16.6342L5.43589 14.9352C5.6017 14.7633 5.69485 14.5301 5.69485 14.287V8.24999C5.69485 5.52474 7.60672 3.26245 10.1157 2.82604V2.74999Z"
                        fill="" />
                    </svg>
                    <span></span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notification">
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="assets/images/lead/lead-6.png" alt="" />
                        </div>
                        <div class="content">
                          <h6>
                            John Doe
                            <span class="text-regular">
                              comment on a product.
                            </span>
                          </h6>
                          <p>
                            Lorem ipsum dolor sit amet, consect etur adipiscing
                            elit Vivamus tortor.
                          </p>
                          <span>10 mins ago</span>
                        </div>
                      </a>
                    </li>
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="assets/images/lead/lead-1.png" alt="" />
                        </div>
                        <div class="content">
                          <h6>
                            Jonathon
                            <span class="text-regular">
                              like on a product.
                            </span>
                          </h6>
                          <p>
                            Lorem ipsum dolor sit amet, consect etur adipiscing
                            elit Vivamus tortor.
                          </p>
                          <span>10 mins ago</span>
                        </div>
                      </a>
                    </li>
                  </ul>
                </div>
                <!-- notification end -->
                <!-- message start -->
                <div class="header-message-box ml-15 d-none d-md-flex">
                  <button class="dropdown-toggle" type="button" id="message" data-bs-toggle="dropdown"
                    aria-expanded="false">
                    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M7.74866 5.97421C7.91444 5.96367 8.08162 5.95833 8.25005 5.95833C12.5532 5.95833 16.0417 9.4468 16.0417 13.75C16.0417 13.9184 16.0364 14.0856 16.0259 14.2514C16.3246 14.138 16.6127 14.003 16.8883 13.8482L19.2306 14.629C19.7858 14.8141 20.3141 14.2858 20.129 13.7306L19.3482 11.3882C19.8694 10.4604 20.1667 9.38996 20.1667 8.25C20.1667 4.70617 17.2939 1.83333 13.75 1.83333C11.0077 1.83333 8.66702 3.55376 7.74866 5.97421Z"
                        fill="" />
                      <path
                        d="M14.6667 13.75C14.6667 17.2938 11.7939 20.1667 8.25004 20.1667C7.11011 20.1667 6.03962 19.8694 5.11182 19.3482L2.76946 20.129C2.21421 20.3141 1.68597 19.7858 1.87105 19.2306L2.65184 16.8882C2.13062 15.9604 1.83338 14.89 1.83338 13.75C1.83338 10.2062 4.70622 7.33333 8.25004 7.33333C11.7939 7.33333 14.6667 10.2062 14.6667 13.75ZM5.95838 13.75C5.95838 13.2437 5.54797 12.8333 5.04171 12.8333C4.53545 12.8333 4.12504 13.2437 4.12504 13.75C4.12504 14.2563 4.53545 14.6667 5.04171 14.6667C5.54797 14.6667 5.95838 14.2563 5.95838 13.75ZM9.16671 13.75C9.16671 13.2437 8.7563 12.8333 8.25004 12.8333C7.74379 12.8333 7.33338 13.2437 7.33338 13.75C7.33338 14.2563 7.74379 14.6667 8.25004 14.6667C8.7563 14.6667 9.16671 14.2563 9.16671 13.75ZM11.4584 14.6667C11.9647 14.6667 12.375 14.2563 12.375 13.75C12.375 13.2437 11.9647 12.8333 11.4584 12.8333C10.9521 12.8333 10.5417 13.2437 10.5417 13.75C10.5417 14.2563 10.9521 14.6667 11.4584 14.6667Z"
                        fill="" />
                    </svg>
                    <span></span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="message">
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="assets/images/lead/lead-5.png" alt="" />
                        </div>
                        <div class="content">
                          <h6>Jacob Jones</h6>
                          <p>Hey!I can across your profile and ...</p>
                          <span>10 mins ago</span>
                        </div>
                      </a>
                    </li>
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="assets/images/lead/lead-3.png" alt="" />
                        </div>
                        <div class="content">
                          <h6>John Doe</h6>
                          <p>Would you mind please checking out</p>
                          <span>12 mins ago</span>
                        </div>
                      </a>
                    </li>
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="assets/images/lead/lead-2.png" alt="" />
                        </div>
                        <div class="content">
                          <h6>Anee Lee</h6>
                          <p>Hey! are you available for freelance?</p>
                          <span>1h ago</span>
                        </div>
                      </a>
                    </li>
                  </ul>
                </div>
                <!-- message end -->
                <!-- profile start -->
                <div class="profile-box ml-15">
                  <button class="dropdown-toggle bg-transparent border-0" type="button" id="profile"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="profile-info">
                      <div class="info">
                        <div class="image">
                          <img src="assets/images/profile/profile-image.png" alt="" />
                        </div>
                        <div>
                          <h6 class="fw-500">Adam Joe</h6>
                          <p>Admin</p>
                        </div>
                      </div>
                    </div>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profile">
                    <li>
                      <div class="author-info flex items-center !p-1">
                        <div class="image">
                          <img src="assets/images/profile/profile-image.png" alt="image">
                        </div>
                        <div class="content">
                          <h4 class="text-sm">Adam Joe</h4>
                          <a class="text-black/40 dark:text-white/40 hover:text-black dark:hover:text-white text-xs"
                            href="#"><EMAIL></a>
                        </div>
                      </div>
                    </li>
                    <li class="divider"></li>
                    <li>
                      <a href="#0">
                        <i class="lni lni-user"></i> View Profile
                      </a>
                    </li>
                    <li>
                      <a href="#0">
                        <i class="lni lni-alarm"></i> Notifications
                      </a>
                    </li>
                    <li>
                      <a href="#0"> <i class="lni lni-inbox"></i> Messages </a>
                    </li>
                    <li>
                      <a href="#0"> <i class="lni lni-cog"></i> Settings </a>
                    </li>
                    <li class="divider"></li>
                    <li>
                      <a href="#0"> <i class="lni lni-exit"></i> Sign Out </a>
                    </li>
                  </ul>
                </div>
                <!-- profile end -->
              </div>
            </div>
          </div>
        </div>
      </header>
      <!-- ========== header end ========== -->

      <!-- ========== button components start ========== -->
      <section class="button-components">
        <div class="container-fluid">
          <!-- ========== title-wrapper start ========== -->
          <div class="title-wrapper pt-30">
            <div class="row align-items-center">
              <div class="col-md-6">
                <div class="title">
                  <h2>Buttons</h2>
                </div>
              </div>
              <!-- end col -->
              <div class="col-md-6">
                <div class="breadcrumb-wrapper">
                  <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                      <li class="breadcrumb-item">
                        <a href="#0">Dashboard</a>
                      </li>
                      <li class="breadcrumb-item">
                        <a href="#0">UI Components</a>
                      </li>
                      <li class="breadcrumb-item active" aria-current="page">
                        Buttons
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
              <!-- end col -->
            </div>
            <!-- end row -->
          </div>
          <!-- ========== title-wrapper end ========== -->

          <!-- ========== button-cards-wrapper start ========== -->
          <div class="buttons-cards-wrapper">
            <div class="row">
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">Square Buttons</h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn square-btn btn-hover">Primary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn square-btn btn-hover">Secondary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn square-btn btn-hover">Success</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn square-btn btn-hover">Danger</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn square-btn btn-hover">Warning</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn square-btn btn-hover">Info</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn square-btn btn-hover">Dark</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn square-btn btn-hover">Light</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn square-btn btn-hover">Active</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn square-btn btn-hover">Deactive</a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">
                    Default Buttons
                    <span class="text-sm text-regular">(3px Corner Round)</span>
                  </h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn btn-hover">Primary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn btn-hover">Secondary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn btn-hover">Success</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn btn-hover">Danger</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn btn-hover">Warning</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn btn-hover">Info</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn btn-hover">Dark</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn btn-hover">Light</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn btn-hover">Active</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn btn-hover">Deactive</a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">
                    Rounded Buttons
                    <span class="text-sm text-regular">(Full Rounded)</span>
                  </h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn rounded-full btn-hover">Primary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn rounded-full btn-hover">Secondary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn rounded-full btn-hover">Success</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn rounded-full btn-hover">Danger</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn rounded-full btn-hover">Warning</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn rounded-full btn-hover">Info</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn rounded-full btn-hover">Dark</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn rounded-full btn-hover">Light</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn rounded-full btn-hover">Active</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn rounded-full btn-hover">Deactive</a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">Square Outline Buttons</h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn-outline square-btn btn-hover">Primary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn-outline square-btn btn-hover">Secondary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn-outline square-btn btn-hover">Success</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn-outline square-btn btn-hover">Danger</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn-outline square-btn btn-hover">Warning</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn-outline square-btn btn-hover">Info</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn-outline square-btn btn-hover">Dark</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn-outline square-btn btn-hover">Light</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn-outline square-btn btn-hover">Active</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn-outline square-btn btn-hover">Deactive</a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">
                    Default Outline Buttons
                    <span class="text-sm text-regular">(3 Px Round)</span>
                  </h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn-outline btn-hover">Primary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn-outline btn-hover">Secondary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn-outline btn-hover">Success</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn-outline btn-hover">Danger</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn-outline btn-hover">Warning</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn-outline btn-hover">Info</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn-outline btn-hover">Dark</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn-outline btn-hover">Light</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn-outline btn-hover">Active</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn-outline btn-hover">Deactive</a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">
                    Default Outline Buttons
                    <span class="text-sm text-regular">(Full Rounded)</span>
                  </h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn-outline rounded-full btn-hover">Primary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn-outline rounded-full btn-hover">Secondary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn-outline rounded-full btn-hover">Success</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn-outline rounded-full btn-hover">Danger</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn-outline rounded-full btn-hover">Warning</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn-outline rounded-full btn-hover">Info</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn-outline rounded-full btn-hover">Dark</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn-outline rounded-full btn-hover">Light</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn-outline rounded-full btn-hover">Active</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn-outline rounded-full btn-hover">Deactive</a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">Square Light Buttons</h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn-light square-btn btn-hover">Primary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn-light square-btn btn-hover">Secondary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn-light square-btn btn-hover">Success</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn-light square-btn btn-hover">Danger</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn-light square-btn btn-hover">Warning</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn-light square-btn btn-hover">Info</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn-light square-btn btn-hover">Dark</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn-light square-btn btn-hover">Light</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn-light square-btn btn-hover">Active</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn-light square-btn btn-hover">Deactive</a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">
                    Default Light Buttons
                    <span class="text-sm text-regular">(3px Corner Round)</span>
                  </h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn-light btn-hover">Primary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn-light btn-hover">Secondary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn-light btn-hover">Success</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn-light btn-hover">Danger</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn-light btn-hover">Warning</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn-light btn-hover">Info</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn-light btn-hover">Dark</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn-light btn-hover">Light</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn-light btn-hover">Active</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn-light btn-hover">Deactive</a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">
                    Rounded Light Buttons
                    <span class="text-sm text-regular">(Full Rounded)</span>
                  </h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn-light rounded-full btn-hover">Primary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn-light rounded-full btn-hover">Secondary</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn-light rounded-full btn-hover">Success</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn-light rounded-full btn-hover">Danger</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn-light rounded-full btn-hover">Warning</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn-light rounded-full btn-hover">Info</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn-light rounded-full btn-hover">Dark</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn-light rounded-full btn-hover">Light</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn-light rounded-full btn-hover">Active</a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn-light rounded-full btn-hover">Deactive</a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">Square Buttons With Icon</h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Primary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Secondary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Success
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Danger
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Warning
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Info
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Dark
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Light
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Active
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Deactive
                      </a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">
                    Default Buttons With Icon
                    <span class="text-sm text-regular">(3px Corner Round)</span>
                  </h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Primary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Secondary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Success
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Danger
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Warning
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Info
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Dark
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Light
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Active
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Deactive
                      </a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">
                    Rounded Buttons With Icon
                    <span class="text-sm text-regular">(Full Rounded)</span>
                  </h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Primary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Secondary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Success
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Danger
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Warning
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Info
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Dark
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Light
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Active
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Deactive
                      </a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">Square Light Buttons With Icon</h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn-light square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Primary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn-light square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Secondary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn-light square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Success
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn-light square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Danger
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn-light square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Warning
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn-light square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Info
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn-light square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Dark
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn-light square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Light
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn-light square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Active
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn-light square-btn btn-hover">
                        <i class="lni lni-heart"></i>
                        Deactive
                      </a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">
                    Default Light Buttons With Icon
                    <span class="text-sm text-regular">(3px Corner Round)</span>
                  </h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn-light btn-hover">
                        <i class="lni lni-heart"></i>
                        Primary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn-light btn-hover">
                        <i class="lni lni-heart"></i>
                        Secondary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn-light btn-hover">
                        <i class="lni lni-heart"></i>
                        Success
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn-light btn-hover">
                        <i class="lni lni-heart"></i>
                        Danger
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn-light btn-hover">
                        <i class="lni lni-heart"></i>
                        Warning
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn-light btn-hover">
                        <i class="lni lni-heart"></i>
                        Info
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn-light btn-hover">
                        <i class="lni lni-heart"></i>
                        Dark
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn-light btn-hover">
                        <i class="lni lni-heart"></i>
                        Light
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn-light btn-hover">
                        <i class="lni lni-heart"></i>
                        Active
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn-light btn-hover">
                        <i class="lni lni-heart"></i>
                        Deactive
                      </a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <div class="col-lg-6">
                <div class="card-style mb-30">
                  <h5 class="text-medium mb-25">
                    Rounded Light Buttons With Icon
                    <span class="text-sm text-regular">(Full Rounded)</span>
                  </h5>
                  <ul class="buttons-group">
                    <li>
                      <a href="#0" class="main-btn primary-btn-light rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Primary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn secondary-btn-light rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Secondary
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn success-btn-light rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Success
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn danger-btn-light rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Danger
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn warning-btn-light rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Warning
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn info-btn-light rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Info
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn dark-btn-light rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Dark
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn light-btn-light rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Light
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn active-btn-light rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Active
                      </a>
                    </li>
                    <li>
                      <a href="#0" class="main-btn deactive-btn-light rounded-full btn-hover">
                        <i class="lni lni-heart"></i>
                        Deactive
                      </a>
                    </li>
                  </ul>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
            </div>
            <!-- end row -->
          </div>
          <!-- ========== button-cards-wrapper end ========== -->
        </div>
        <!-- end container -->
      </section>
      <!-- ========== button components end ========== -->

      <!-- ========== footer start =========== -->
      <footer class="footer">
        <div class="container-fluid">
          <div class="row">
            <div class="col-md-6 order-last order-md-first">
              <div class="copyright text-center text-md-start">
                <p class="text-sm">
                  Designed and Developed by
                  <a href="https://plainadmin.com" rel="nofollow" target="_blank">
                    PlainAdmin
                  </a>
                </p>
              </div>
            </div>
            <!-- end col-->
            <div class="col-md-6">
              <div class="terms d-flex justify-content-center justify-content-md-end">
                <a href="#0" class="text-sm">Term & Conditions</a>
                <a href="#0" class="text-sm ml-15">Privacy & Policy</a>
              </div>
            </div>
          </div>
          <!-- end row -->
        </div>
        <!-- end container -->
      </footer>
      <!-- ========== footer end =========== -->
    </main>
    <!-- ======== main-wrapper end =========== -->

    <!-- ========= All Javascript files linkup ======== -->
    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/Chart.min.js"></script>
    <script src="assets/js/dynamic-pie-chart.js"></script>
    <script src="assets/js/moment.min.js"></script>
    <script src="assets/js/fullcalendar.js"></script>
    <script src="assets/js/jvectormap.min.js"></script>
    <script src="assets/js/world-merc.js"></script>
    <script src="assets/js/polyfill.js"></script>
    <script src="assets/js/main.js"></script>
  </body>
</html>
