<?php
// Test data insertion script for CDAS system
require '../core/dbcon.ini';

try {
    // Insert sample students into cdas_student table
    $students = [
        [
            'Student_ID' => '123456',
            'Candidate_ID' => '123456',
            'Last_Name' => '<PERSON>',
            'First_Name' => '<PERSON>',
            'Middle_Name' => 'Cruz',
            'Department' => 'College',
            'Course' => 'AB PHILO',
            'Year' => '4',
            'Scholarship' => 'REG',
            'Entry_Year' => '2021'
        ],
        [
            'Student_ID' => '728373',
            'Candidate_ID' => '123334',
            'Last_Name' => 'Matienzo',
            'First_Name' => '<PERSON>',
            'Middle_Name' => 'Chico',
            'Department' => 'College',
            'Course' => 'BSIT',
            'Year' => '4',
            'Scholarship' => 'REG',
            'Entry_Year' => '2021'
        ],
        [
            'Student_ID' => '34455',
            'Candidate_ID' => '324344',
            'Last_Name' => '<PERSON><PERSON><PERSON>',
            'First_Name' => '<PERSON>',
            'Middle_Name' => 'Orno',
            'Department' => 'College',
            'Course' => 'BSIT',
            'Year' => '4',
            'Scholarship' => 'REG',
            'Entry_Year' => '2021'
        ],
        [
            'Student_ID' => '45433',
            'Candidate_ID' => '23434',
            'Last_Name' => 'Mata',
            'First_Name' => 'Nessa Mae',
            'Middle_Name' => 'Gaerlan',
            'Department' => 'College',
            'Course' => 'BSIT',
            'Year' => '3',
            'Scholarship' => 'REG',
            'Entry_Year' => '2022'
        ],
        [
            'Student_ID' => '344551',
            'Candidate_ID' => '4545434',
            'Last_Name' => 'Manguiat',
            'First_Name' => 'Jimwel',
            'Middle_Name' => 'Madali',
            'Department' => 'College',
            'Course' => 'BSIT',
            'Year' => '4',
            'Scholarship' => 'REG',
            'Entry_Year' => '2021'
        ]
    ];

    // Check if cdas_student table exists, if not create it
    $createStudentTable = "
    CREATE TABLE IF NOT EXISTS cdas_student (
        Student_ID VARCHAR(20) PRIMARY KEY,
        Candidate_ID VARCHAR(20),
        Last_Name VARCHAR(100),
        First_Name VARCHAR(100),
        Middle_Name VARCHAR(100),
        Department VARCHAR(100),
        Course VARCHAR(100),
        Year VARCHAR(10),
        Scholarship VARCHAR(50),
        Entry_Year VARCHAR(10)
    )";
    
    $db1->exec($createStudentTable);

    // Check if cdas_attendance table exists, if not create it
    $createAttendanceTable = "
    CREATE TABLE IF NOT EXISTS cdas_attendance (
        id INT AUTO_INCREMENT PRIMARY KEY,
        Student_ID VARCHAR(20),
        ip_address VARCHAR(45),
        time_in DATETIME NULL,
        time_out DATETIME NULL,
        os VARCHAR(100),
        browser VARCHAR(100),
        FOREIGN KEY (Student_ID) REFERENCES cdas_student(Student_ID)
    )";

    $db1->exec($createAttendanceTable);

    // Insert students
    $stmt = $db1->prepare("INSERT IGNORE INTO cdas_student (Student_ID, Candidate_ID, Last_Name, First_Name, Middle_Name, Department, Course, Year, Scholarship, Entry_Year) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    foreach ($students as $student) {
        $stmt->execute([
            $student['Student_ID'],
            $student['Candidate_ID'],
            $student['Last_Name'],
            $student['First_Name'],
            $student['Middle_Name'],
            $student['Department'],
            $student['Course'],
            $student['Year'],
            $student['Scholarship'],
            $student['Entry_Year']
        ]);
    }

    echo "<h2>✅ Test Data Inserted Successfully!</h2>";
    echo "<h3>Students Added:</h3>";
    echo "<ul>";
    foreach ($students as $student) {
        echo "<li>{$student['First_Name']} {$student['Last_Name']} (ID: {$student['Student_ID']})</li>";
    }
    echo "</ul>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Go to <a href='register.php'>register.php</a> to register your device with a Student ID</li>";
    echo "<li>Then go to <a href='attendance.php'>attendance.php</a> to test the Time In/Time Out system</li>";
    echo "</ol>";
    
    echo "<p><strong>Sample Student IDs to test:</strong> 123456, 728373, 34455, 45433, 344551</p>";

} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
