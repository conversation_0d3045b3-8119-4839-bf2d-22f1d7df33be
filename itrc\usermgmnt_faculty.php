<?php
    include '../core/dbcon.ini';
	include '../assets/common/header.php';
    include '../assets/common/design.php';
    include '../query/users.qry'; 
    include '../query/menus.qry'; 

    $usersfnc = new USERSFNC;
    $menusfnc = new MENUS;
    
	$page_title = "Menu Management";
    $current_page = "User Management";
	
	if(isset($_POST['save_adduser']))
	{
        $fullnanme=$_POST['facultyLastName'].' '.$_POST['facultyFirstName'].' '.$_POST['facultyMiddleName'];
		$lastname=$_POST['facultyLastName'];
        $firstname=$_POST['facultyFirstName'];
        $middlename=$_POST['facultyMiddleName'];
        $username=$_POST['facultyUsername'];
        $password=$_POST['facultyPassword'];
        $position=$_POST['facultyPosition'];
        $module=$_POST['facultyModule'];
		
		$usersfnc->ins_useraccounts($db1,$fullnanme, $lastname, $firstname, $middlename, $username, $password, $position, $module);
        header("Location: usermgmnt_faculty.php");
        exit();
	}

    if(isset($_POST['save_edituser']))
    {
        $uaid = $_POST['uaid'];
        $fullname = $_POST['lastName'].' '.$_POST['firstName'].' '.$_POST['middleName'];
        $lastname = $_POST['lastName'];
        $firstname = $_POST['firstName'];
        $middlename = $_POST['middleName'];
        $username = $_POST['username'];
        $password = $_POST['password'];
        $position = $_POST['position'];
        $modid = $_POST['modid'];
        $status = $_POST['status'];
        
        $usersfnc->upd_useraccounts($db1, $uaid, $fullname, $lastname, $firstname, $middlename, $username, $password, $position, $modid, $status);
        header("Location: usermgmnt_faculty.php");
        exit();
    }
	
    if (isset($_POST['save_menu_changes'])) {
        $uaid = $_POST['uaid'];
        $menus = isset($_POST['menus']) ? $_POST['menus'] : [];
        $submenus = isset($_POST['submenus']) ? $_POST['submenus'] : [];
        $user = $usersfnc->get_user_by_uaid($db1, $uaid);
        $modid = $user['modid'];
        $menusfnc->delete_userpassport($db1, $uaid);
        $allsubmenus = $menusfnc->get_allsubmenu($db1);
        
        for ($i = 0; $i < count($submenus); $i++) {
            $submenuid = $submenus[$i];
            $menuid = 0;
            for ($j = 0; $j < count($allsubmenus); $j++) {
                if ($allsubmenus[$j]['submenuid'] == $submenuid) {
                    $menuid = $allsubmenus[$j]['menuid'];
                }
            }
            if ($menuid != 0) {
                $menusfnc->insert_userpassport($db1, $uaid, $modid, $menuid, $submenuid);
            }
        }
    
        for ($i = 0; $i < count($menus); $i++) {
            $menuid = $menus[$i];
            $haschild = false;
            for ($j = 0; $j < count($allsubmenus); $j++) {
                 if ($allsubmenus[$j]['menuid'] == $menuid) {
                    $haschild = true;
                }
            }
            if (!$haschild) {
                 $menusfnc->insert_userpassport($db1, $uaid, $modid, $menuid, 0);
            }
        }
    }

    $records_per_page = 10;
    $pagination_current_page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $offset = ($pagination_current_page - 1) * $records_per_page;

    $total_users = $usersfnc->get_total_useraccounts_count($db1);
    $total_pages = ceil($total_users / $records_per_page);

	 $alluseraccounts = $usersfnc->get_alluseraccounts($db1, $records_per_page, $offset);
    $allmodules = $usersfnc->get_allmodule($db1);
    $all_menus = $menusfnc->get_allmenu($db1);
    $all_submenus = $menusfnc->get_allsubmenu($db1);
   
?>
<?php include '../assets/common/title.php'; ?>
 <div class="main-container" id="main-container"  >
  <?php include '../assets/common/sidebar.php'; ?>
  </div>
  
  <main class="main-wrapper">
	 <?php require '../assets/common/topbar.php'; ?>
	 
	 <section class="section">
        <div class="container-fluid">
          <!-- ========== title-wrapper start ========== -->
          <div class="title-wrapper pt-10">
            <div class="row align-items-center">
              <div class="col-md-6">
                <div class="title">
                  <h2><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?></h2>
                </div>
              </div>
              <!-- end col -->
              <div class="col-md-6">
                <div class="breadcrumb-wrapper">
                  <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                      <li class="breadcrumb-item">
                        <a href="#0">Dashboard</a>
                      </li>
                      <li class="breadcrumb-item active" aria-current="page">
                       User Management
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
			  <!-- ========== tables-wrapper start ========== -->
               <div class="row">
              <div class="col-lg-12">
                <div class="card-style mb-30">
                  <h6 class="mb-10">
                    <?php //echo isset($current_page) ? $current_page : 'User Management'; ?>
                  </h6>
                  
                  <div class="d-flex justify-content-between align-items-center mb-4">
                    <!-- <div class="search-wrapper">
                      <input type="text" class="form-control" placeholder="Search...">
                    </div> -->
                    <div class="d-flex align-items-center w-100">
                      <h6 class="mb-0 flex-grow-1"><?php echo isset($current_page) ? $current_page : 'User Management'; ?></h6>
                      <div class="buttons-group ms-auto">
                        <ul class="d-flex justify-content-end mb-0">
                          <li>
                            <a href="#0" class="main-btn primary-btn rounded-full btn-hover" 
                            style="padding: 10px 40px; font-size: 15px; background-color:#1E2F97;" 
                            data-bs-toggle="modal" data-bs-target="#addFacultyModal">Add User</a>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div class="table-wrapper table-responsive">
                    <table class="table"  id="tableone">
                        
                      <thead>
                        <tr>
					            	<th>No.</th>
                        <th>Fullname</th>
                        <th>Username</th>
                        <th>Password</th>
                        <th>Status</th>
                        <th>Action</th>
                        </tr>
                        <!-- end table row-->
                      </thead>
                      <tbody>
                             <?php
                            $start_index = ($pagination_current_page - 1) * $records_per_page;
                            for($i=0;$i<=count($alluseraccounts)-1;$i++)
                            {
                                echo '<tr>';
                                echo '<td>'.($start_index + $i + 1).'</td>';
                                echo '<td>'.$alluseraccounts[$i]['fullname'].'</td>';
                                echo '<td>'.$alluseraccounts[$i]['username'].'</td>';
                                echo '<td>'.$alluseraccounts[$i]['password'].'</td>';
                                $status = ($alluseraccounts[$i]['status'] == 0) ? "Active" : "Inactive";
                                echo '<td>'.$status.'</td>';
                                
                                $userpassport = $menusfnc->get_userpassport($db1, $alluseraccounts[$i]['uaid']);
                                $menuids = [];
                                $submenuids = [];
                                for ($j = 0; $j < count($userpassport); $j++) {
                                    $pass = $userpassport[$j];
                                    $menuids[] = $pass['menuid'];
                                    if ($pass['submenuid'] != "0" && $pass['submenuid'] !== null) {
                                        $submenuids[] = $pass['submenuid'];
                                    }
                                }
                                $menustr = implode(',', array_unique($menuids));
                                $submenustr = implode(',', array_unique($submenuids));

                                echo '<td>
                                    <div class="action d-flex justify-content-center">
                                    <button class="text-danger edit-user-btn" data-bs-toggle="modal" data-bs-target="#editUserModal"
                                        data-uaid="'.$alluseraccounts[$i]['uaid'].'"
                                        data-fullname="'.$alluseraccounts[$i]['fullname'].'"
                                        data-lastname="'.$alluseraccounts[$i]['lastname'].'"
                                        data-firstname="'.$alluseraccounts[$i]['firstname'].'"
                                        data-middlename="'.$alluseraccounts[$i]['middlename'].'"
                                        data-username="'.$alluseraccounts[$i]['username'].'"
                                        data-password="'.$alluseraccounts[$i]['password'].'"
                                        data-position="'.$alluseraccounts[$i]['position'].'"
                                        data-modid="'.$alluseraccounts[$i]['modid'].'"
                                        data-status="'.$alluseraccounts[$i]['status'].'">
                                        <i class="lni lni-pencil-alt"  style="color:#ffffff;"></i>
                                    </button>
                                    <button class="view-user-menu-btn" title="View" data-bs-toggle="modal" data-bs-target="#viewMenuModal" data-uaid="'.$alluseraccounts[$i]['uaid'].'" data-menus="'.$menustr.'" data-submenus="'.$submenustr.'">
                                    <i class="lni lni-eye" style="color:#ffffff;"></i>
                                    </button>
                                    </div>
                                </td>';
						              	echo '</tr>';
                            }
                          ?>
                          
                        <!-- end table row -->
                          
                     
                        <!-- end table row -->
                      </tbody>
                    </table>
                    <!-- end table -->
                  </div>
					<!-- Pagination Controls -->
					<nav class="mt-3 d-flex justify-content-end">
					  <ul class="pagination mb-0">
						<li class="page-item <?php if($pagination_current_page <= 1) echo 'disabled'; ?>">
						  <a class="page-link" href="?page=<?php echo $pagination_current_page - 1; ?>">Previous</a>
						</li>
						<?php for($i = 1; $i <= $total_pages; $i++): ?>
						<li class="page-item <?php if($pagination_current_page == $i) echo 'active'; ?>">
						  <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
						</li>
						<?php endfor; ?>
						<li class="page-item <?php if($pagination_current_page >= $total_pages) echo 'disabled'; ?>">
						  <a class="page-link" href="?page=<?php echo $pagination_current_page + 1; ?>">Next</a>
						</li>
					  </ul>
					</nav>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
            </div>
            <!-- end row -->
          </div>
         
          <!-- ========== tables-wrapper end ========== -->
			 
              <!-- end col -->
            </div>
            <!-- end row -->
			
          </div>
		</div>
	</section> 
<!-- View Menu Modal -->
<div class="modal fade" id="viewMenuModal" tabindex="-1" aria-labelledby="viewMenuModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form id="menuForm" method="POST" action="usermgmnt_faculty.php">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewMenuModalLabel">User Menu Access</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="uaid" id="viewUaid">
                    <div id="menuContainer">
                        <?php
                            for ($i = 0; $i < count($all_menus); $i++) {
                                $menu = $all_menus[$i];
                                echo '<div class="menu-section mb-4">';
                                echo '<div class="form-check checkbox-style checkbox-warning mb-2">';
                                echo '<input class="form-check-input main-menu" type="checkbox" name="menus[]" value="' . $menu['menuid'] . '" id="menu-' . $menu['menuid'] . '">';
                                echo '<label class="form-check-label fw-bold" for="menu-' . $menu['menuid'] . '">' . $menu['menutitle'] . '</label>';
                                echo '</div>';
                                
                                echo '<div class="ms-4">';
                                for ($j = 0; $j < count($all_submenus); $j++) {
                                    $submenu = $all_submenus[$j];
                                    if ($submenu['menuid'] == $menu['menuid']) {
                                        echo '<div class="form-check checkbox-style checkbox-warning mb-2">';
                                        echo '<input class="form-check-input sub-menu" type="checkbox" name="submenus[]" value="' . $submenu['submenuid'] . '" id="submenu-' . $submenu['submenuid'] . '">';
                                        echo '<label class="form-check-label" for="submenu-' . $submenu['submenuid'] . '">' . $submenu['submenutitle'] . '</label>';
                                        echo '</div>';
                                    }
                                }
                                echo '</div>';
                                echo '</div>';
                            }
                        ?>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" name="save_menu_changes" class="btn btn-primary">Save changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

  <!-- Edit User Modal -->
  <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg"><!-- Added modal-lg for larger width -->
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form method="POST" action="usermgmnt_faculty.php">
            <input type="hidden" id="editUaid" name="uaid">
            <div class="mb-3">
              <label for="editLastName" class="form-label">Last Name</label>
              <input type="text" class="form-control" id="editLastName" name="lastName">
            </div>
            <div class="mb-3">
              <label for="editFirstName" class="form-label">First Name</label>
              <input type="text" class="form-control" id="editFirstName" name="firstName">
            </div>
            <div class="mb-3">
              <label for="editMiddleName" class="form-label">Middle Name</label>
              <input type="text" class="form-control" id="editMiddleName" name="middleName">
            </div>
            <div class="mb-3">
              <label for="editUsername" class="form-label">Username</label>
              <input type="text" class="form-control" id="editUsername" name="username">
            </div>
            <div class="mb-3">
              <label for="editPassword" class="form-label">Password</label>
              <input type="password" class="form-control" id="editPassword" name="password">
            </div>
            <div class="mb-3">
              <label for="editPosition" class="form-label">Position</label>
              <select class="form-control" id="editPosition" name="position">
                <option value="Program Head">Program Head</option>
                <option value="Dean">Dean</option>
                <option value="VDTA">VDTA</option>
                <option value="VDAA">VDAA</option>
                <option value="OSA">OSA</option>
                <option value="Instructor">Instructor</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="editModule" class="form-label">Module</label>
              <select class="form-control" id="editModule" name="modid">
                <option value="">Select Module</option>
                <?php
                for ($i = 0; $i <= count($allmodules)-1; $i++) {
                    echo '<option value="' . $allmodules[$i]['modid'] . '">' . $allmodules[$i]['module'] . '</option>';
                }
                ?>
              </select>
            </div>
            <div class="mb-3">
              <label for="editStatus" class="form-label">Status</label>
              <select class="form-control" id="editStatus" name="status">
                <option value="0">Active</option>
                <option value="1">Inactive</option>
              </select>
            </div>
          
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <button type="submit" name="save_edituser" class="btn btn-primary">Save changes</button>
        </div>
		</form>
      </div>
    </div>
  </div>

  <!-- Add Faculty Modal -->
  <div class="modal fade" id="addFacultyModal" tabindex="-1" aria-labelledby="addFacultyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addFacultyModalLabel">Add New Users</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form method="POST">
            <div class="mb-3">
              <label for="facultyLastName" class="form-label">Last Name</label>
              <input type="text" class="form-control" id="facultyLastName" name="facultyLastName" required>
            </div>
            <div class="mb-3">
              <label for="facultyFirstName" class="form-label">First Name</label>
              <input type="text" class="form-control" id="facultyFirstName" name="facultyFirstName" require>
            </div>
            <div class="mb-3">
              <label for="facultyMiddleName" class="form-label">Middle Name</label>
              <input type="text" class="form-control" id="facultyMiddleName" name="facultyMiddleName" required>
            </div>
            <div class="mb-3">
              <label for="facultyUsername" class="form-label">Username</label>
              <input type="text" class="form-control" id="facultyUsername" name="facultyUsername" required>
            </div>
            <div class="mb-3">
              <label for="facultyPassword" class="form-label">Password</label>
              <input type="password" class="form-control" id="facultyPassword" name="facultyPassword" required>
            </div>
            <div class="mb-3">
              <label for="facultyPosition" class="form-label">Position</label>
              <select class="form-control" id="facultyPosition" name="facultyPosition" required>
                <option value="">Select Position</option>
                <option value="Program Head">Program Head</option>
                <option value="Dean">Dean</option>
                <option value="VDTA">VDTA</option>
                <option value="VDAA">VDAA</option>
                <option value="OSA">OSA</option>
                <option value="Instructor">Instructor</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="facultyModule" class="form-label">Module</label>
              <select class="form-control" id="facultyModule" name="facultyModule"  required>
                <option value="">Select Module</option>
                <?php
                for ($i = 0; $i <= count($allmodules)-1; $i++) {
                    echo '<option value="' . $allmodules[$i]['modid'] . '">' . $allmodules[$i]['module'] . '</option>';
                }
                ?>
              </select>
            </div>
         
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <button type="submit" name="save_adduser" class="btn btn-primary">Save changes</button>
        </div>
		</form>
      </div>
    </div>
  </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    function setupMainMenuListeners() {
        document.querySelectorAll('.main-menu').forEach(mainMenu => {
            mainMenu.addEventListener('change', function() {
                const subMenus = this.closest('.menu-section').querySelectorAll('.sub-menu');
                subMenus.forEach(subMenu => {
                    subMenu.checked = this.checked;
                });
            });
        });

        document.querySelectorAll('.menu-section').forEach(section => {
            const subMenus = section.querySelectorAll('.sub-menu');
            const mainMenu = section.querySelector('.main-menu');

            if (mainMenu && subMenus.length > 0) {
                 subMenus.forEach(subMenu => {
                    subMenu.addEventListener('change', function() {
                        const allSubMenus = Array.from(subMenus);
                        const allChecked = allSubMenus.every(sm => sm.checked);
                        mainMenu.checked = allChecked;
                    });
                });
            }
        });
    }

    document.querySelectorAll('.view-user-menu-btn').forEach(button => {
        button.addEventListener('click', function() {
            const uaid = this.dataset.uaid;
            document.getElementById('viewUaid').value = uaid;

            document.querySelectorAll('#menuContainer .form-check-input').forEach(cb => {
                cb.checked = false;
            });

            const userMenuSet = new Set((this.dataset.menus || '').split(','));
            const userSubmenuSet = new Set((this.dataset.submenus || '').split(','));

            document.querySelectorAll('#menuContainer .sub-menu').forEach(checkbox => {
                if (userSubmenuSet.has(checkbox.value)) {
                    checkbox.checked = true;
                }
            });

            document.querySelectorAll('.menu-section').forEach(section => {
                const subMenus = section.querySelectorAll('.sub-menu');
                const mainMenu = section.querySelector('.main-menu');

                if (mainMenu && subMenus.length > 0) {
                    const allSubMenus = Array.from(subMenus);
                    const allChecked = allSubMenus.every(sm => sm.checked);
                    mainMenu.checked = allChecked;
                } else if (mainMenu) {
                    if (userMenuSet.has(mainMenu.value)) {
                        mainMenu.checked = true;
                    }
                }
            });
        });
    });

    setupMainMenuListeners();

    // Populate Edit User Modal
    document.querySelectorAll('.edit-user-btn').forEach(button => {
        button.addEventListener('click', function() {
            const uaid = this.dataset.uaid;
            const lastName = this.dataset.lastname;
            const firstName = this.dataset.firstname;
            const middleName = this.dataset.middlename;
            const username = this.dataset.username;
            const password = this.dataset.password;
            const position = this.dataset.position;
            const modid = this.dataset.modid;
            const status = this.dataset.status;

            document.getElementById('editUaid').value = uaid;
            document.getElementById('editLastName').value = lastName;
            document.getElementById('editFirstName').value = firstName;
            document.getElementById('editMiddleName').value = middleName;
            document.getElementById('editUsername').value = username;
            document.getElementById('editPassword').value = password;
            document.getElementById('editPosition').value = position;
            document.getElementById('editModule').value = modid;
            document.getElementById('editStatus').value = status;
        });
    });
});
</script>

  <?php include '../assets/common/footer.php'; ?>
</main>
  <?php include '../assets/common/scripts.php'; ?>
  
 
</body>
</html>
