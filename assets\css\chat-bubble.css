/* Floating Chat Bubble */
.chat-bubble-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 998;
}

.chat-bubble-toggle {
    width: 60px;
    height: 60px;
    background-color: #1E2F97;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.9);
}

.chat-bubble-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(250, 250, 251, 0.4);
}

.chat-bubble-toggle i {
    transition: transform 0.3s ease;
}

.chat-bubble-toggle.active i {
    transform: rotate(180deg);
}

/* Dark mode styles for chat bubble */
[data-bs-theme="dark"] .chat-bubble-toggle {
    background-color: #5a6268;
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

/* Sliding Chat Panel */
.chat-panel {
    position: fixed;
    top: 20px;
    right: -100%;
    width: 400px;
    height: calc(100vh - 40px);
    background: #ffffff;
    box-shadow: -2px 0 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    border-radius: 20px;
    margin: 0 20px;
    visibility: hidden;
    opacity: 0;
    font-size: 16px;
    overflow: hidden;
}

.chat-panel.active {
    right: 0;
    visibility: visible;
    opacity: 1;
}

[data-bs-theme="dark"] .chat-panel {
    background: #121212;
    box-shadow: -2px 0 20px rgba(0, 0, 0, 0.5);
}

.chat-panel.expanded {
    width: 600px;
}

/* Animation for expand/collapse */
.chat-panel, .chat-expand-btn i {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Controls container */
.chat-panel-controls {
    display: flex;
    gap: 0.5rem;
}

/* Expand/Contract button */
.chat-expand-btn {
    width: 32px;
    height: 32px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.chat-expand-btn i {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    transition: all 0.3s ease;
}

[data-bs-theme="dark"] .chat-expand-btn {
    color: #999;
}

/* Fix for expand/collapse button icons */
.chat-expand-btn .lni-frame-expand,
.chat-expand-btn .lni-frame-contract {
    width: 18px;
    height: 18px;
    display: inline-block;
    transition: all 0.2s ease;
}

.chat-panel.expanded .chat-expand-btn .lni-frame-expand {
    display: none;
}

.chat-panel.expanded .chat-expand-btn .lni-frame-contract {
    display: inline-block;
}

.chat-panel:not(.expanded) .chat-expand-btn .lni-frame-expand {
    display: inline-block;
}

.chat-panel:not(.expanded) .chat-expand-btn .lni-frame-contract {
    display: none;
}

/* Dark mode support for expand button */
body.dark-mode .chat-expand-btn,
[data-bs-theme="dark"] .chat-expand-btn {
    color: #e0e0e0;
}

.chat-close-btn,
.chat-clear-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

/* Hover effects for header buttons */
.chat-expand-btn:hover,
.chat-close-btn:hover,
.chat-clear-btn:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
    color: #333;
}

[data-bs-theme="dark"] .chat-expand-btn:hover,
[data-bs-theme="dark"] .chat-close-btn:hover,
[data-bs-theme="dark"] .chat-clear-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

/* Chat Panel Header */
.chat-panel-header {
    padding: 1rem;
    background: rgba(255, 255, 255, 1);
    color: #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 5;
}

[data-bs-theme="dark"] .chat-panel-header {
    background: #121212;
    color: white;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-panel-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 1.2rem;
}

/* Messages Area */
.chat-panel-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    background-color: #f8fafc;
    margin-top: -1px;
    padding-top: 1rem;
}

[data-bs-theme="dark"] .chat-panel-messages {
    background-color: #121212;
    background-image: none;
}

/* Message container with avatar */
.message-container {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.5rem;
    gap: 4px;
}

.message-container.sent {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    margin-top: 4px;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.chat-panel .message-bubble {
    max-width: calc(70% - 32px);
    padding: 0.625rem 1rem;
    border-radius: 18px;
    position: relative;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    line-height: 1.4;
    font-size: 0.9375rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    margin: 0;
    transition: all 0.2s ease;
}

/* Message styles */
.chat-panel .message-sent {
    background: #f0f0f0;
    color: #000;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.chat-panel .message-received {
    background: #fff;
    color: #1e293b;
    margin-right: auto;
    border-bottom-left-radius: 4px;
}

/* Dark mode message styles */
[data-bs-theme="dark"] .chat-panel .message-sent {
    background: #2d2d2d;
    color: #e0e0e0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

[data-bs-theme="dark"] .chat-panel .message-received {
    background: #1e1e1e;
    color: #e0e0e0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Message hover effects */
.message-bubble:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

/* Time indicators */
.chat-time {
    font-size: 0.6875rem;
    margin-top: 0.25rem;
    opacity: 0.8;
    color: inherit;
}

/* Chat Input Area - Messenger Style */
.chat-panel-input {
    padding: 0.75rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background: white;
}

[data-bs-theme="dark"] .chat-panel-input {
    background: #121212;
    border-top-color: rgba(255, 255, 255, 0.05);
}

/* Modern input style based on the image */
.chat-panel-input .input-group.modern-input {
    background: #f0f2f5;
    display: flex;
    align-items: center;
    border-radius: 24px;
    padding: 0 0.375rem 0 1.25rem;
    margin: 0 0.5rem;
    transition: all 0.2s ease;
}

/* Dark mode style for the modern input */
[data-bs-theme="dark"] .chat-panel-input .input-group.modern-input {
    background: #1e1e1e;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 24px;
    padding: 0.25rem 0.5rem 0.25rem 1rem;
}

/* Hover effect for the input field in dark mode */
[data-bs-theme="dark"] .chat-panel-input .input-group.modern-input:hover {
    background: #252525;
    border-color: rgba(255, 255, 255, 0.15);
}

/* Ensure text in the input field is visible in dark mode */
[data-bs-theme="dark"] .chat-panel-input .input-group.modern-input .form-control,
body.dark-mode .chat-panel-input .input-group.modern-input .form-control {
    color: #ffffff !important;
    background-color: transparent !important;
    caret-color: #ffffff !important; /* Make cursor visible */
}

/* Override any conflicting styles from main.css */
body.dark-mode .chat-panel-input .form-control {
    background: transparent !important;
    color: #ffffff !important;
}

/* Ensure dark mode styles are applied regardless of selector */
body.dark-mode .chat-panel-input .input-group.modern-input {
    background: #1e1e1e !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    border-radius: 24px !important;
    padding: 0.25rem 0.5rem 0.25rem 1rem !important;
}

/* Hover effect for dark mode */
body.dark-mode .chat-panel-input .input-group.modern-input:hover {
    background: #252525 !important;
    border-color: rgba(255, 255, 255, 0.15) !important;
}

/* Button styling for dark mode */
body.dark-mode .input-action-btn {
    background: transparent !important;
    color: #808080 !important;
}

body.dark-mode .input-action-btn:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #e0e0e0 !important;
}

/* New style for the input field based on the image */
.chat-panel-input .input-group.modern-input .form-control {
    border: none;
    border-radius: 0;
    padding: 0.75rem 0;
    background: transparent;
    transition: all 0.2s ease;
    flex: 1;
    font-size: 0.9375rem;
    color: #1e293b;
    box-shadow: none;
    margin: 2px 0;
}

.chat-panel-input .input-group.modern-input .form-control::placeholder {
    color: #94a3b8;
    font-size: 0.9rem;
}

.chat-panel-input .input-group.modern-input:hover {
    background: #e9ecef;
}

[data-bs-theme="dark"] .chat-panel-input .input-group.modern-input .form-control::placeholder {
    color: #808080;
    opacity: 0.8;
}

/* Action buttons container */
.chat-panel-input .input-actions {
    display: flex;
    align-items: center;
}

/* Input action buttons */
.input-action-btn {
    background: transparent;
    border: none;
    color: #64748b;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    padding: 0;
    margin: 0 2px;
}

.input-action-btn i {
    font-size: 1.25rem;
    transition: transform 0.2s ease;
}

.input-action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: scale(1.1);
}

.input-action-btn:hover i {
    transform: translateX(2px);
}

/* Dark mode input action buttons */
[data-bs-theme="dark"] .input-action-btn {
    color: #808080;
}

[data-bs-theme="dark"] .input-action-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #e0e0e0;
}

/* Refined message bubbles for dark mode */
[data-bs-theme="dark"] .chat-panel .message-received {
    background: #1e1e1e;
    color: #e0e0e0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    border-radius: 18px;
    border-bottom-left-radius: 4px;
}

[data-bs-theme="dark"] .chat-panel .message-sent {
    background: #2d2d2d;
    color: #e0e0e0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    border-radius: 18px;
    border-bottom-right-radius: 4px;
}

/* Time indicator in dark mode */
[data-bs-theme="dark"] .chat-time {
    color: #a0a0a0;
    opacity: 0.7;
}

/* Scrollbar Styling */
.chat-panel-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-panel-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-panel-messages::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

[data-bs-theme="dark"] .chat-panel-messages::-webkit-scrollbar-thumb {
    background: #333;
}

[data-bs-theme="dark"] .chat-panel-messages::-webkit-scrollbar-thumb:hover {
    background: #444;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    gap: 4px;
}

.typing-indicator .message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    margin-top: 4px;
}

.typing-indicator .message-bubble {
    max-width: calc(70% - 32px);
    padding: 0.625rem 1rem;
    border-radius: 18px;
    position: relative;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    background: #fff;
    color: #1e293b;
    margin-right: auto;
    border-bottom-left-radius: 4px;
}

[data-bs-theme="dark"] .typing-indicator .message-bubble {
    background: #1e1e1e;
    color: #e0e0e0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.typing-dots {
    display: flex;
    align-items: center;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background-color: #64748b;
    border-radius: 50%;
    opacity: 0.6;
    animation: typingAnimation 1.4s infinite ease-in-out;
}

[data-bs-theme="dark"] .typing-dot {
    background-color: #94a3b8;
}

.typing-dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingAnimation {
    0%, 100% {
        transform: translateY(0);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-4px);
        opacity: 1;
    }
}

/* Animation for message bubbles */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(8px); }
    to { opacity: 1; transform: translateY(0); }
}

.message-bubble {
    animation: fadeIn 0.2s ease-out forwards;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-panel {
        position: fixed;
        top: 0;
        right: -100%;
        left: 0;
        width: 100%;
        height: 100vh;
        margin: 0;
        border-radius: 0;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .chat-panel.active {
        right: 0;
    }

    .chat-panel-header {
        padding: 15px;
        border-radius: 0;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    [data-bs-theme="dark"] .chat-panel-header {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    }

    .chat-panel-messages {
        flex: 1;
        padding: 15px;
        height: calc(100vh - 120px);
        padding-bottom: 80px; 
        overflow-y: auto;
        -webkit-overflow-scrolling: touch; 
    }

    .chat-panel-input {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 10px 15px;
        background: #fff;
        border-radius: 0;
        z-index: 10; 
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
    }

    [data-bs-theme="dark"] .chat-panel-input {
        background: #121212;
        box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.3);
    }

    .message-bubble {
        max-width: 85%;
        padding: 10px 15px;
        margin-bottom: 10px;
        font-size: 0.95rem;
    }
    
    /* Ensure typing indicator is always visible */
    .typing-indicator {
        padding-bottom: 10px;
        margin-bottom: 10px;
    }
}

@media (max-width: 480px) {
    .chat-panel {
        font-size: 13px;
    }

    .chat-panel-header h5 {
        font-size: 1rem;
    }

    .message-bubble {
        max-width: 90%;
        padding: 8px 12px;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .chat-bubble-toggle {
        width: 50px;
        height: 50px;
        font-size: 20px;
        bottom: 15px;
        right: 15px;
    }
}

/* Mobile Responsiveness */
@media screen and (max-width: 768px) {
    .chat-panel {
        position: fixed;
        top: 0;
        right: -100%;
        left: 0;
        width: 100%;
        height: 100vh;
        margin: 0;
        transform: none;
        visibility: hidden;
        opacity: 0;
    }

    .chat-panel.active {
        right: 0;
        visibility: visible;
        opacity: 1;
        transform: none;
    }
}

/* Hide chat bubble and expand button on mobile */
@media (max-width: 768px) {
    .chat-bubble-container {
        display: none !important;
    }
    
    .chat-expand-btn {
        display: none !important;
    }
}

/* Ensure chat panel takes full height on mobile */
@media (max-width: 768px) {
    .chat-panel.active {
        height: 100vh !important;
        width: 100vw !important;
        bottom: 0 !important;
        right: 0 !important;
        border-radius: 0 !important;
    }
}

/* File Preview Styles - Modern Design */
.file-preview-area {
    display: none;
    margin: 0 12px 12px;
    padding: 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selected-file {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.selected-file:hover {
    background: #f0f0f0;
}

.selected-file img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 8px;
    margin-right: 12px;
}

.selected-file i.file-icon {
    font-size: 24px;
    color: #4a7bff;
    margin-right: 12px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(74, 123, 255, 0.1);
    border-radius: 8px;
}

.selected-file span {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.remove-file {
    color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.1);
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin-left: 8px;
    transition: all 0.2s ease;
}

.remove-file:hover {
    background: rgba(220, 53, 69, 0.2);
    transform: scale(1.1);
}

.remove-file i {
    font-size: 16px;
}

/* Dark mode styles for file preview */
body.dark-mode .file-preview-area,
[data-bs-theme="dark"] .file-preview-area {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

body.dark-mode .selected-file,
[data-bs-theme="dark"] .selected-file {
    background: #1e1e1e;
    border-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .selected-file:hover,
[data-bs-theme="dark"] .selected-file:hover {
    background: #252525;
}

body.dark-mode .selected-file span,
[data-bs-theme="dark"] .selected-file span {
    color: #e0e0e0;
}

body.dark-mode .selected-file i.file-icon,
[data-bs-theme="dark"] .selected-file i.file-icon {
    color: #6690ff;
    background: rgba(74, 123, 255, 0.15);
}

body.dark-mode .remove-file,
[data-bs-theme="dark"] .remove-file {
    background: rgba(220, 53, 69, 0.15);
    color: #ff6b6b !important;
}

body.dark-mode .remove-file:hover,
[data-bs-theme="dark"] .remove-file:hover {
    background: rgba(220, 53, 69, 0.25);
}
