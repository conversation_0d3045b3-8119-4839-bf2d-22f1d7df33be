<?php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$base_url = "/cdas/"; 

if (!isset($_SESSION['uaid'])) {
    header("Location: ../login.php");
    exit;
} 

include '../core/dbcon.ini';
$userId = $_SESSION['uaid'];

try {
    // Updated to fetch modid and position
    $stmt = $db1->prepare("SELECT * FROM useraccounts WHERE uaid = ?");
    $stmt->execute([$userId]);
    
    if ($stmt->rowCount() > 0) {
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        $modid = $row['modid'];
        $_SESSION['modid'] = $modid;
        $_SESSION['usertype'] = $row['position']; // Store as usertype to match login.php
    }
} catch(PDOException $e) {
    // Session error
    header("Location: ../login.php");
    exit;
}

$session_script_path = $_SERVER['PHP_SELF'];
$admin_pages = strpos($session_script_path, '/itrc/') !== false;
$class_teacher_pages = strpos($session_script_path, '/staff/') !== false;

// Check admin access (modid should be 0 for admin)
if ($admin_pages && $_SESSION['modid'] != 0 ) {
    header("Location: ../login.php");
    exit;
}

// Check staff access (modid should be 1 for staff)
if ($class_teacher_pages && $_SESSION['modid'] != 1) {
    header("Location: ../login.php");
    exit;
}

$expiry = 1800; 
if (isset($_SESSION['LAST']) && (time() - $_SESSION['LAST'] > $expiry)) {
    session_unset();
    session_destroy();
    header("Location: ../login.php?timeout");
    exit;
}
$_SESSION['LAST'] = time();
?>
