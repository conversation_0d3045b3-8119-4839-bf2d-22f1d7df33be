<?php
	include '../core/dbcon.ini';
	include '../assets/common/header.php';
	include '../assets/common/design.php';
  include '../query/students.qry';

  $studfnc=new STUDENTSFNC;
	
	$page_title = "User Management - Student";
  $current_page = "Home";

  $allstudents=$studfnc->get_allstudents($db1);
?>

 <body>
 <?php include '../assets/common/title.php'; ?>

 <div class="main-container" id="main-container"  >
  <?php include '../assets/common/sidebar.php'; ?>
  </div>
  
  <main class="main-wrapper">
	 <?php require '../assets/common/topbar.php'; ?>
	 
	 <section class="section">
        <div class="container-fluid">
          <!-- ========== title-wrapper start ========== -->
          <div class="title-wrapper pt-10">
            <div class="row align-items-center">
              <div class="col-md-6">
                <div class="title">
                  <h2><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?></h2>
                </div>
              </div>
              <!-- end col -->
              <div class="col-md-6">
                <div class="breadcrumb-wrapper">
                  <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                      <li class="breadcrumb-item">
                        <a href="#0">Dashboard</a>
                      </li>
                      <li class="breadcrumb-item active" aria-current="page">
                       <?php echo isset($current_page) ? $current_page : ''; ?>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
			  <!-- ========== tables-wrapper start ========== -->
               <div class="row">
              <div class="col-lg-12">
                <div class="card-style mb-30">
                  <h6 class="mb-10">Student list</h6>
                    
                  <div class="table-wrapper table-responsive">
                    <table class="table" id="tableone">
                      <thead>
                        <tr>
                          <th>Picture</th>
                          <th>Fullname</th>
                          <th>Course</th>
                          <th>Status</th>
                        </tr>
                        <!-- end table row-->
                      </thead>
                      <tbody>
                        <tr>
                           <!--<td>
                           <div class="employee-image" style="position: relative; left: 50%; transform: translateX(-50%); width: 50px; height: 50px;">
                              <img src="../assets/images/lead/lead-1.png" alt=""  />
                            </div>
                          </td>-->
                         
                            <?php
                            for($i=0;$i<=count($allstudents)-1;$i++)
                            {
								echo '<tr>';
								echo '<td>  
                                <div class="employee-image" style="position: relative; left: 50%; transform: translateX(-50%); width: 50px; height: 50px;">
                                    <img src="../assets/images/lead/lead-1.png" alt=""  />
                                </div>
                                </td>';
								echo '<td>'.$allstudents[$i]['Last_Name'].' '.$allstudents[$i]['First_Name'].'</td>';
								echo '<td>'.$allstudents[$i]['Course'].'</td>';
								echo '<td>                          
                                <select class="form-select" id="status_row1">
                                    <option value="active">Active</option>
                                    <option value="inactive" selected>Inactive</option>
                                </select></td>';
								echo '</tr>';
							}
                            /*<option value="">BSIT</option>
                            <option value="">TVET AUTOMOTIVE TECHNOLOGY</option>
                            <option value="">TVET MECHANICAL TECHNOLOGY</option>*/
                            ?>
                           <!-- Esther Howard
                          </td>
                          <td>
                            BSIT
                          </td>
                          <td>
                            <select class="form-select" id="status_row1">
                                <option value="active">Active</option>
                                <option value="inactive" selected>Inactive</option>
                            </select>
                          </td>-->
                        </tr>
                        <!-- end table row -->
                        <!--<tr>
                          <td>
                            <div class="employee-image" style="position: relative; left: 50%; transform: translateX(-50%); width: 50px; height: 50px;">
                              <img src="../assets/images/lead/lead-2.png" alt=""  />
                            </div>
                          </td>
                          <td class="min-width">
                            D. Jonathon
                          </td>
                          <td class="min-width">
                            BSIT
                          </td>
                          <td class="min-width">
                            <select class="form-select" id="status_row2">
                                <option value="active">Active</option>
                                <option value="inactive" selected>Inactive</option>
                            </select>
                          </td>
                        </tr>-->
                        <!-- end table row -->
                        <!-- <tr>
                          <td>
                            <div class="employee-image" style="position: relative; left: 50%; transform: translateX(-50%); width: 50px; height: 50px;">
                              <img src="../assets/images/lead/lead-3.png" alt=""  />
                            </div>
                          </td>
                          <td class="min-width"> 
                            John Doe
                          </td>
                          <td class="min-width"> 
                            BSIT
                          </td>
                          <td class="min-width">
                            <select class="form-select" id="status_row3">
                                <option value="active">Active</option>
                                <option value="inactive" selected>Inactive</option>
                            </select>
                          </td>
                        </tr>
                         end table row 
                        <tr>
                          <td>
                            <div class="employee-image" style="position: relative; left: 50%; transform: translateX(-50%); width: 50px; height: 50px;">
                              <img src="../assets/images/lead/lead-4.png" alt=""  />
                            </div>
                          </td>
                          <td class="min-width"> 
                            Rayhan Jamil
                          </td>
                          <td class="min-width"> 
                            BSIT
                          </td>
                          <td class="min-width">
                            <select class="form-select" id="status_row4">
                                <option value="active">Active</option>
                                <option value="inactive" selected>Inactive</option>
                            </select>
                          </td>
                        </tr>
                         end table row 
                        <tr>
                          <td>
                            <div class="employee-image" style="position: relative; left: 50%; transform: translateX(-50%); width: 50px; height: 50px;">
                              <img src="../assets/images/lead/lead-5.png" alt="" />
                            </div>
                          </td>
                          <td class="min-width"> 
                            Esther Howard
                          </td>
                          <td class="min-width"> 
                            BSIT
                          </td>
                          <td class="min-width">
                            <select class="form-select" id="status_row5">
                                <option value="active">Active</option>
                                <option value="inactive" selected>Inactive</option>
                            </select>
                          </td>
                        </tr>
                         end table row 
                        <tr>
                          <td>
                            <div class="employee-image" style="position: relative; left: 50%; transform: translateX(-50%); width: 50px; height: 50px;">
                              <img src="../assets/images/lead/lead-6.png" alt=""  />
                            </div>
                          </td>
                          <td class="min-width"> 
                            Anee Doe
                          </td>
                          <td class="min-width"> 
                            BSIT
                          </td>
                          <td class="min-width">
                            <select class="form-select" id="status_row6">
                                <option value="active">Active</option>
                                <option value="inactive" selected>Inactive</option>
                            </select>
                          </td>
                        </tr> -->
                        <!-- end table row -->
                      </tbody>
                    </table>
                    <!-- end table -->
                  </div>
                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
            </div>
            <!-- end row -->
          </div>
         
          <!-- ========== tables-wrapper end ========== -->
			 
              <!-- end col -->
            </div>
            <!-- end row -->
			
          </div>
		</div>
	</section> 
  <!-- Edit Student Modal 
  <div class="modal fade" id="editStudentModal" tabindex="-1" aria-labelledby="editStudentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="editStudentModalLabel">Edit Student Details</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form>
            <div class="mb-3">
              <label for="studentName" class="form-label">Name</label>
              <input type="text" class="form-control" id="studentName">
            </div>
            <div class="mb-3">
              <label for="studentEmail" class="form-label">Email</label>
              <input type="email" class="form-control" id="studentEmail">
            </div>
            <div class="mb-3">
              <label for="studentProject" class="form-label">Project</label>
              <input type="text" class="form-control" id="studentProject">
            </div>
            <div class="mb-3">
              <label for="studentStatus" class="form-label">Status</label>
              <select class="form-select" id="studentStatus">
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
          <button type="button" class="btn btn-primary">Save changes</button>
        </div>
      </div>
    </div>
  </div> -->

  <?php include '../assets/common/footer.php'; ?>
</main>
  <?php include '../assets/common/scripts.php'; ?>
  
 
</body>
</html>
