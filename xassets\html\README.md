# PlainAdmin - Free Vanilla JS Bootstrap 5 Admin Dashboard Template

PlainAdmin is a free and open-source **Vanilla JS admin dashboard template** based on Bootstrap 5 that comes with 5 unique dashboard variations, 300+ essential dashboard components, pages, UI elements, charts, graphs, application UI pages, and more. to provide everything you need to build a data-rich backend or admin panel for almost any sort of web app.

[![plainadmindemo](https://github.com/PlainAdmin/plain-free-bootstrap-admin-template/blob/main/plainadmin.jpg)](https://demo.plainadmin.com/)


### [🚀 View Demo](https://demo.plainadmin.com/)

### [✨ Visit Website](https://plainadmin.com/)

### [⬇️ Download Now](https://plainadmin.com/download)

### [📄 Documentation/Installation](https://plainadmin.com/docs/)

### [⚡ PRO Version](https://plainadmin.com/pricing)


It comes with a minimal UI design that suits almost any kind of web project and offers the best possible user experience. PlainAdmin comes with SCSS files, an organized codebase, gulp support for efficient workflow, rich documentation, and everything else that you can expect from a modern admin template.

PlainAdmin is built with vanilla Javascript (no jQuery), Bootstrap 5 and the simplest possible way to make it easy to maintain and easy to port your back-end projects.

### [👉 Check out our Admin Template for Tailwind CSS](https://tailadmin.com)

If you are looking for a high-quality free admin template that comes with all essential dashboard components and features then, PlainAdmin is the perfect choice for you.

## [📄 Documentation](https://plainadmin.com/docs/)
- [Installation](https://plainadmin.com/docs/#installation)
- [Quick Start](https://plainadmin.com/docs/#quick-start)
- [Layout and Theme](https://plainadmin.com/docs/#layout-theme)
- [Colors](https://plainadmin.com/docs/#colors)
- [Alerts](https://plainadmin.com/docs/#alerts)
- [Buttons](https://plainadmin.com/docs/#buttons)
- [Cards](https://plainadmin.com/docs/#cards)
- [Tabs](https://plainadmin.com/docs/#tabs)
- [Forms](https://plainadmin.com/docs/#forms)
- [Icons](https://plainadmin.com/docs/#icons)
- [Tables](https://plainadmin.com/docs/#tables)
- [Credits](https://plainadmin.com/docs/#credits)

### Update Logs - 2.0 :
- Rebranded the entire template.
- Updated the style guide
- The sidebar menu icons have been updated.
- The overall design of the sidebar has been enhanced.
- The header menu icons have been updated.
- The design of the header dropdown has been updated.
- Additional buttons have been added to the Button Page.
- Updated essential dependencies
- The charts code has been adjusted to accommodate the updated version.
- UX and UI enhancements

### Update Logs - 1.2 :
- Added Kanban (App) [PRO]
- Added File Manager (App) [PRO]
- Dependencies updated
- FullCalender updated

### Update Logs - 1.1.2 :
- FullCalender updated
- Dependencies updates

### Update Logs - 1.1.1 :
- Updated to v5.1.3 (Latest)
- Fixed minor bugs
- Changed primary color
- Multiple sidebar variations
- Improved UX
- Optimized codebase

### Update Logs - 1.1 :
- Updated to Bootstrap 5.1.1
- Fixed minor bugs
- Enhanced the UI and Improved Typography
