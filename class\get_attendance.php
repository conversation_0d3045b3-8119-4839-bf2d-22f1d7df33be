<?php
    include '../core/dbcon.ini';
    include '../assets/common/header.php';
    include '../assets/common/design.php';

    $page_title = "Get Attendance Records";
    $current_page = "Get Attendance";


    $courses_years = [
        'BSIT 1' => 'BSIT 1st Year',
        'BSIT 2' => 'BSIT 2nd Year', 
        'BSIT 3' => 'BSIT 3rd Year',
        'BSIT 4' => 'BSIT 4th Year',
        'Automotive 1' => 'Automotive 1st Year',
        'Automotive 2' => 'Automotive 2nd Year',
        'Automotive 3' => 'Automotive 3rd Year',
        'Automotive 4' => 'Automotive 4th Year',
        'Electrical 1' => 'Electrical 1st Year',
        'Electrical 2' => 'Electrical 2nd Year',
        'Electrical 3' => 'Electrical 3rd Year',
        'Mechanical 1' => 'Mechanical 1st Year',
        'Mechanical 2' => 'Mechanical 2nd Year',
        'Civil 1' => 'Civil Engineering 1st Year',
        'Civil 2' => 'Civil Engineering 2nd Year'
    ];

   
    $sample_attendance = [
        'BSIT 3' => [
            ['student_id' => 'BSIT-2022-001', 'name' => '<PERSON>', 'time_in' => '08:15 AM', 'time_out' => '11:45 AM', 'status' => 'Present'],
            ['student_id' => 'BSIT-2022-002', 'name' => '<PERSON> Vert Matienzo', 'time_in' => '08:20 AM', 'time_out' => '11:45 AM', 'status' => 'Late'],
            ['student_id' => 'BSIT-2022-003', 'name' => 'John Ralph Jordico', 'time_in' => '08:10 AM', 'time_out' => '11:45 AM', 'status' => 'Present'],
            ['student_id' => 'BSIT-2022-004', 'name' => 'Nessa Mae Mata', 'time_in' => '08:25 AM', 'time_out' => '11:45 AM', 'status' => 'Late'],
            ['student_id' => 'BSIT-2022-005', 'name' => 'Jimwel Manguiat', 'time_in' => '08:05 AM', 'time_out' => '11:45 AM', 'status' => 'Present']
        ],
        'Automotive 1' => [
            ['student_id' => 'AUTO-2024-001', 'name' => 'Mark Santos', 'time_in' => '07:30 AM', 'time_out' => '10:30 AM', 'status' => 'Present'],
            ['student_id' => 'AUTO-2024-002', 'name' => 'Juan Dela Cruz', 'time_in' => '07:45 AM', 'time_out' => '10:30 AM', 'status' => 'Late'],
            ['student_id' => 'AUTO-2024-003', 'name' => 'Maria Garcia', 'time_in' => '07:25 AM', 'time_out' => '10:30 AM', 'status' => 'Present']
        ],
        'Automotive 2' => [
            ['student_id' => 'AUTO-2023-001', 'name' => 'Pedro Reyes', 'time_in' => '09:00 AM', 'time_out' => '12:00 PM', 'status' => 'Present'],
            ['student_id' => 'AUTO-2023-002', 'name' => 'Ana Villanueva', 'time_in' => '09:15 AM', 'time_out' => '12:00 PM', 'status' => 'Late']
        ],
        'Electrical 1' => [
            ['student_id' => 'ELEC-2024-001', 'name' => 'Carlos Mendoza', 'time_in' => '08:00 AM', 'time_out' => '11:00 AM', 'status' => 'Present'],
            ['student_id' => 'ELEC-2024-002', 'name' => 'Lisa Torres', 'time_in' => '08:10 AM', 'time_out' => '11:00 AM', 'status' => 'Present']
        ]
    ];

    // Handle AJAX request for getting attendance
    if (isset($_POST['action']) && $_POST['action'] == 'get_attendance') {
        $course_year = $_POST['course_year'] ?? '';
        $date = $_POST['date'] ?? '';
        
        $records = isset($sample_attendance[$course_year]) ? $sample_attendance[$course_year] : [];
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'records' => $records,
            'course_year' => $course_year,
            'date' => $date,
            'count' => count($records)
        ]);
        exit;
    }
?>

<body>
<?php include '../assets/common/title.php'; ?>

<div class="main-container" id="main-container">
    <?php include '../assets/common/sidebar.php'; ?>
</div>

<main class="main-wrapper">
    <?php require '../assets/common/topbar.php'; ?>

    <section class="section">
        <div class="container-fluid">
            <!-- ========== title-wrapper start ========== -->
            <div class="title-wrapper pt-10">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="title">
                            <h2 style="font-size: 24px;"><?php echo $page_title; ?></h2>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="breadcrumb-wrapper">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="index.php">My Classes</a>
                                    </li>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <?php echo $current_page; ?>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                </div>
                <!-- end row -->

                <!-- Back Button -->
                <div class="row mb-20">
                    <div class="col-12">
                        <a href="index.php" class="main-btn secondary-btn btn-hover" style="background-color: #6c757d; border-color: #6c757d; border-radius: 8px; padding: 10px 20px; font-size: 14px; text-decoration: none; color: white;">
                            ← Back to Dashboard
                        </a>
                    </div>
                </div>

                <div class="row">
                    <!-- Filter Section -->
                    <div class="col-lg-4">
                        <div class="card-style mb-30">
                            <div class="title d-flex justify-content-between align-items-center">
                                <div class="left">
                                    <h6 class="text-medium mb-2" style="font-size: 18px;">📋 Get Attendance</h6>
                                    <p class="text-sm text-gray" style="font-size: 14px;">Filter per course, per year</p>
                                </div>
                            </div>

                            <form id="attendanceForm">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="select-style-1 mb-20">
                                            <label style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">Course & Year</label>
                                            <select id="courseYear" name="course_year" style="font-size: 14px; padding: 12px;">
                                                <option value="">Select Course & Year</option>
                                                <?php foreach ($courses_years as $key => $value): ?>
                                                    <option value="<?php echo $key; ?>">
                                                        <?php echo $value; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="input-style-1 mb-20">
                                            <label style="font-size: 14px; font-weight: 500; margin-bottom: 8px;">Date</label>
                                            <input type="date" id="attendanceDate" name="date" value="<?php echo date('Y-m-d'); ?>" style="font-size: 14px; padding: 12px;">
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <button type="button" class="main-btn primary-btn btn-hover w-100" onclick="getAttendance()" style="background-color: #004AAD; border-color: #004AAD; border-radius: 8px; padding: 12px 24px; font-size: 14px; font-weight: 500;">
                                            📊 Get Attendance Records
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <!-- Sample Course Examples -->
                            <div class="mt-30">
                                <h6 style="font-size: 14px; margin-bottom: 15px; color: #004AAD;">📝 Course Examples:</h6>
                                <div class="d-flex flex-wrap gap-2">
                                    <span class="badge" style="background-color: #e3f2fd; color: #004AAD; padding: 6px 10px; border-radius: 6px; font-size: 12px;">BSIT 3</span>
                                    <span class="badge" style="background-color: #e8f5e8; color: #4CAF50; padding: 6px 10px; border-radius: 6px; font-size: 12px;">Automotive 1</span>
                                    <span class="badge" style="background-color: #fff3e0; color: #FFC300; padding: 6px 10px; border-radius: 6px; font-size: 12px;">Electrical 2</span>
                                    <span class="badge" style="background-color: #ffebee; color: #FF4B4B; padding: 6px 10px; border-radius: 6px; font-size: 12px;">Mechanical 1</span>
                                </div>
                                <p class="text-gray mt-10" style="font-size: 12px;">
                                    Example: "Automotive 1" = 1st year Automotive students
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div class="col-lg-8">
                        <div class="card-style mb-30" id="attendanceResults" style="display: none;">
                            <div class="title d-flex justify-content-between align-items-center">
                                <div class="left">
                                    <h6 class="text-medium mb-2" style="font-size: 18px;">📊 Attendance Records</h6>
                                    <p class="text-sm text-gray" id="resultsInfo" style="font-size: 14px;"></p>
                                </div>
                                <div class="right">
                                    <button class="main-btn success-btn btn-hover" onclick="exportAttendance()" style="background-color: #4CAF50; border-color: #4CAF50; border-radius: 8px; padding: 8px 16px; font-size: 12px;">
                                        📥 Export Excel
                                    </button>
                                </div>
                            </div>

                            <div class="table-wrapper table-responsive">
                                <table class="table" id="attendanceTable">
                                    <thead>
                                        <tr>
                                            <th style="font-size: 14px; font-weight: 600;">Student ID</th>
                                            <th style="font-size: 14px; font-weight: 600;">Name</th>
                                            <th style="font-size: 14px; font-weight: 600;">Time In</th>
                                            <th style="font-size: 14px; font-weight: 600;">Time Out</th>
                                            <th style="font-size: 14px; font-weight: 600;">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="attendanceTableBody">
                                        <!-- Results will be populated here -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Default Message -->
                        <div class="card-style mb-30" id="defaultMessage">
                            <div class="text-center" style="padding: 40px;">
                                <div style="font-size: 48px; margin-bottom: 20px;">📋</div>
                                <h5 style="font-size: 18px; margin-bottom: 10px; color: #004AAD;">Select Course & Year</h5>
                                <p class="text-gray" style="font-size: 14px;">
                                    Choose a course and year from the filter to view attendance records.<br>
                                    Example: Select "BSIT 3" to see 3rd year BSIT students.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>
    <?php include '../assets/common/footer.php'; ?>
</main>
<?php include '../assets/common/scripts.php'; ?>

<script>
    function getAttendance() {
        const courseYear = document.getElementById('courseYear').value;
        const date = document.getElementById('attendanceDate').value;
        
        if (!courseYear) {
            alert('Please select a course and year');
            return;
        }
        
        // Hide default message and show results
        document.getElementById('defaultMessage').style.display = 'none';
        document.getElementById('attendanceResults').style.display = 'block';
        document.getElementById('attendanceTableBody').innerHTML = '<tr><td colspan="5" class="text-center">Loading...</td></tr>';
        
        // Make AJAX request
        fetch('get_attendance.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=get_attendance&course_year=${courseYear}&date=${date}`
        })
        .then(response => response.json())
        .then(data => {
            displayAttendanceResults(data);
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('attendanceTableBody').innerHTML = '<tr><td colspan="5" class="text-center text-danger">Error loading data</td></tr>';
        });
    }
    
    function displayAttendanceResults(data) {
        const tbody = document.getElementById('attendanceTableBody');
        const resultsInfo = document.getElementById('resultsInfo');
        
        resultsInfo.textContent = `${data.course_year} - ${new Date(data.date).toLocaleDateString()} (${data.count} students)`;
        
        if (data.records.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-gray">No records found for this course/year</td></tr>';
            return;
        }
        
        tbody.innerHTML = data.records.map(record => {
            const statusClass = record.status === 'Present' ? 'success-btn' : record.status === 'Late' ? 'warning-btn' : 'danger-btn';
            const statusColor = record.status === 'Present' ? '#4CAF50' : record.status === 'Late' ? '#FFC300' : '#FF4B4B';
            
            return `
                <tr>
                    <td style="font-size: 13px;">${record.student_id}</td>
                    <td style="font-size: 13px; font-weight: 500;">${record.name}</td>
                    <td style="font-size: 13px;">${record.time_in}</td>
                    <td style="font-size: 13px;">${record.time_out}</td>
                    <td><span class="status-btn ${statusClass}" style="background-color: ${statusColor}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px;">${record.status}</span></td>
                </tr>
            `;
        }).join('');
    }
    
    function exportAttendance() {
        // Simple CSV export functionality
        const table = document.getElementById('attendanceTable');
        let csv = [];
        
        // Get headers
        const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent);
        csv.push(headers.join(','));
        
        // Get data rows
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        rows.forEach(row => {
            const cells = Array.from(row.querySelectorAll('td')).map(td => td.textContent.trim());
            csv.push(cells.join(','));
        });
        
        // Download CSV
        const csvContent = csv.join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `attendance_${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
    }
</script>

</body>
</html>
