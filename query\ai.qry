<?php
class AIFNC
{
    function get_allstudents($db)
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM cdas_student");
        $stmt->execute();
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function get_student_data($db)
    {
        $students = $this->get_allstudents($db);
        $studentInfo = "Available Student Data:\n";

        foreach ($students as $student) {
            $studentInfo .= "Student ID: " . ($student['Student_ID'] ?? 'N/A') .
                           ", Name: " . ($student['First_Name'] ?? 'N/A') . " " . ($student['Last_Name'] ?? 'N/A') .
                           ", Course: " . ($student['Course'] ?? 'N/A') .
                           ", Year: " . ($student['Year'] ?? 'N/A') . "\n";
        }
        return $studentInfo;
    }


}
?>
