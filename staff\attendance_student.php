<?php
	include '../assets/common/header.php';
	include '../assets/common/design.php';
	include '../core/dbcon.ini';
	include '../query/students.qry';
	
	$studfnc=new STUDENTSFNC;
	
	$page_title = "ITRC Faculty Dashboard"; 
    $current_page = "Home";
	
	$allcourses=$studfnc->get_allcourses($db1);
	
	$allactivity=$studfnc->get_allactivity($db1);
	
	$allstudent=$studfnc->get_allstudentss($db1);

	
?>

 
 <body>
 <?php include '../assets/common/title.php'; ?>

 <div class="main-container" id="main-container"  >
  <?php include '../assets/common/sidebar.php'; ?>
  </div>
  
  <main class="main-wrapper">
	 <?php require '../assets/common/topbar.php'; ?>
	 
	 <section class="section">
        <div class="container-fluid">
          <!-- ========== title-wrapper start ========== -->
          <div class="title-wrapper pt-10">
            <div class="row align-items-center">
              <div class="col-md-6">
                <div class="title">
                  <h2><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?></h2>
                </div>
              </div>
              <!-- end col -->
              <div class="col-md-6">
                <div class="breadcrumb-wrapper">
                  <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                      <li class="breadcrumb-item">
                        <a href="#0">Dashboard</a>
                      </li>
                      <li class="breadcrumb-item active" aria-current="page">
                       <?php echo isset($current_page) ? $current_page : ''; ?>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
			  <!-- ========== tables-wrapper start ========== -->
          <div class="tables-wrapper">
            <div class="row">
              <div class="col-lg-12">
                <div class="card-style mb-30">
				<div class="row">
					<div class="col-lg-4">
						<div class="d-flex align-items-center gap-2">
						<input type="text" class="form-control form-control-sm" placeholder="Search By Last Name" style="width: 200px;">
						<a href="#0" class="btn btn-primary  rounded-pill" style="background-color:#1E2F97; " data-bs-toggle="modal" data-bs-target="#addFacultyModal">VIEW</a>
						</div>
					</div>
					
					<div class="col-lg-4">
						<div class="d-flex align-items-center gap-2">
						  <select class="form-select form-select-sm light-bg" style="max-width: 200px;">
							<?php
							for($i=0;$i<=count($allcourses)-1;$i++)
							{
								echo '<option value="'.$allcourses[$i]['gy_id'].'">'.$allcourses[$i]['gradeyear'].'</option>';
							}
							/*<option value="">BSIT</option>
							<option value="">TVET AUTOMOTIVE TECHNOLOGY</option>
							<option value="">TVET MECHANICAL TECHNOLOGY</option>*/
							?>
						  </select>
						<a href="#0" class="btn btn-primary  rounded-pill" style="background-color:#1E2F97;" data-bs-toggle="modal" data-bs-target="#addFacultyModal">VIEW</a>
						</div>
					</div>
					
					<div class="col-lg-4">
						<div class="d-flex align-items-center gap-2">
						 <select class="form-select form-select-sm light-bg" style="max-width: 200px;">
						 <?php
							for($i=0;$i<=count($allactivity)-1;$i++)
							{
								echo '<option value="'.$allactivity[$i]['activity_id'].'">'.$allactivity[$i]['activity_name'].'</option>';
							}
							/*<option value="">BSIT</option>
							<option value="">TVET AUTOMOTIVE TECHNOLOGY</option>
							<option value="">TVET MECHANICAL TECHNOLOGY</option>*/
							?>
					  <!--<option value="">REGULAR CLASSES</option>
					<option value="">MORNING ASSEMBLY</option>
					<option value="">EPC MASS</option>
					<option value="">DEPARTMENTAL MASS</option>-->
				  </select>
						<a href="#0" class="btn btn-primary  rounded-pill" style="background-color:#1E2F97;" data-bs-toggle="modal" data-bs-target="#addFacultyModal">VIEW</a>
						</div>
					</div>
				</div>
				
                   <!-- ==== data tables <h6 class="mb-10">Data Tables</h6>-->
				    <!-- <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="search-wrapper">
                      <input type="text" class="form-control" placeholder="Search...">
                    </div>
                    <div class="buttons-group">
                      <ul class="d-flex">
                        <li>
                        
                        </li>
                      </ul>
                    </div>
                  </div>-->
				  
			
					  <!-- ====for basic -->
                  <!-- ====dropdown  <p class="text-sm mb-20">
                    For basic styling—light padding and only horizontal
                    dividers—use the class table.
                  </p>  -->
				  
					  <!-- ====for last name search -->
								  
			

<hr />

                  <div class="table-wrapper table-responsive">
                    <table class="table" id="tableone">
                      <thead>
                        <tr>
                          <th class="lead-info">Name</th>
                          <th class="lead-email">Time In</th>
                          <th class="lead-phone">Time Out</th>
                          <th class="lead-company">Course</th>
                          <th>Action</th>
                        </tr>
                        <!-- end table row-->
                      </thead>
                      <tbody>
                        <tr>
                         <!-- <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-1.png" alt="" />
                              </div>
                              <dvi class="lead-text">-->
                        
								<?php
                            for($i=0;$i<=count($allstudent)-1;$i++)
                            {
								echo '<tr>';
								echo '<td>'.$allstudent[$i]['Last_Name'].' '.$allstudent[$i]['First_Name'].', '.$allstudent[$i]['Middle_Name'].'</td>';		
								echo '<td></td>';		
								echo '<td></td>';		
								echo '<td></td>';		
								echo '<td><div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;  margin-left: 15px;"></i>
                              </button>
                            </div></td>';		
									echo '</tr>';
						
                                 
                               
                            }
                          ?>
							 </div>
                            </div>
                          </td>
                        
                        
                          <td>
                      
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-2.png" alt="" />
                              </div>
                              <div class="lead-text">
                                John Doe
                              </div>
                            </div>
                          </td>
                          <td class="min-width">
                             <EMAIL>
                          </td>
                          <td class="min-width">
                         (303)555 3343523
                          </td>
                          <td class="min-width">
                        Graygrids digital agency
                          </td>
                          <td>
                             <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff; margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-3.png" alt="" />
                              </div>
                              <div class="lead-text">
                           David Smith
                              </div>
                            </div>
                          </td>
                          <td class="min-width">
                           <EMAIL>
                          </td>
                          <td class="min-width">
                           (303)555 3343523
                          </td>
                          <td>
                           Abc agency
                          </td>
                          <td>
                         <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                 <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-4.png" alt="" />
                              </div>
                              <div class="lead-text">
                              Jonathon
                              </div>
                            </div>
                          </td>
                          <td class="min-width">
                        <EMAIL>
                          </td>
                          <td class="min-width">
                       (303)555 3343523
                          </td>
                          <td class="min-width">
                        Creative IT Agency
                          </td>
                          <td>
                            <div class="action">
                              <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-5.png" alt="" />
                              </div>
                              <div class="lead-text">
                              Anna Lee
                              </div>
                            </div>
                          </td>
                          <td>
                             <EMAIL>
                          </td>
                          <td>
                            (303)555 3343523
                          </td>
                          <td>
                            Halal It agency
                          </td>
                          <td>
                                                       <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-6.png" alt="" />
                              </div>
                              <div class="lead-text">
                               Gray Simon
                              </div>
                            </div>
                          </td>
                          <td>
                              <EMAIL>
                          </td>
                          <td>
                          (303)555 3343523
                          </td>
                          <td>
                          DesignCourse
                          </td>
                          <td>
                                                        <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-1.png" alt="" />
                              </div>
                              <div class="lead-text">
                           New User 1
                              </div>
                            </div>
                          </td>
                          <td>
                      <EMAIL>
                          </td>
                          <td>
                            (111)222 333444
                          </td>
                          <td>
                         New Agency 1
                          </td>
                          <td>
                                                        <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-2.png" alt="" />
                              </div>
                              <div class="lead-text">
                              New User 2
                              </div>
                            </div>
                          </td>
                          <td>
                              <EMAIL>
                          </td>
                          <td>
								(111)222 333445
                          </td>
                          <td>
								New Agency 2
                          </td>
                          <td>
                                                        <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-3.png" alt="" />
                              </div>
                              <div class="lead-text">
									New User 3
                              </div>
                            </div>
                          </td>
                          <td>
                          <EMAIL>
                          </td>
                          <td>
                          (111)222 333444
                          </td>
                          <td>
                           New Agency 3
                          </td>
                          <td>
                                                        <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-4.png" alt="" />
                              </div>
                              <div class="lead-text">
                              New User 4
                              </div>
                            </div>
                          </td>
                          <td>
                             <EMAIL>
                          </td>
                          <td>
                           (111)222 333447
                          <td>
                           New Agency 4
                          </td>
                          <td>
                                                        <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-5.png" alt="" />
                              </div>
                              <div class="lead-text">
                              New User 5
                              </div>
                            </div>
                          </td>
                          <td>
                           <EMAIL>
                          </td>
                          <td>
                          (111)222 333448
                          </td>
                          <td>
                          New Agency 5
                          </td>
                          <td>
                                                       <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-6.png" alt="" />
                              </div>
                              <div class="lead-text">
                               New User 6
                              </div>
                            </div>
                          </td>
                          <td>
                            <EMAIL>
                          </td>
                          <td>
                           (111)222 333449
                          </td>
                          <td>
                            New Agency 6
                          </td>
                          <td>
                                                        <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-1.png" alt="" />
                              </div>
                              <div class="lead-text">
                               New User 7
                              </div>
                            </div>
                          </td>
                          <td>
                            <EMAIL>
                          </td>
                          <td>
                            (111)222 333450
                          </td>
                          <td>
                            New Agency 7
                          </td>
                          <td>
                                                        <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-2.png" alt="" />
                              </div>
                              <div class="lead-text">
                               New User 8
                              </div>
                            </div>
                          </td>
                          <td>
                              <EMAIL>
                          </td>
                          <td>
                            <p>(111)222 333451</p>
                          </td>
                          <td>
                            <p>New Agency 8</p>
                          </td>
                          <td>
                                                        <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-3.png" alt="" />
                              </div>
                              <div class="lead-text">
                                New User 9
                              </div>
                            </div>
                          </td>
                          <td>
                            <EMAIL>
                          </td>
                          <td>
                            (111)222 333452
                          </td>
                          <td>
                           New Agency 9
                          </td>
                          <td>
                                                       <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-4.png" alt="" />
                              </div>
                              <div class="lead-text">
                               New User 10
                              </div>
                            </div>
                          </td>
                          <td>
                            <EMAIL>
                          </td>
                          <td>
                            (111)222 333453
                          </td>
                          <td>
                           New Agency 10
                          </td>
                          <td>
                                                         <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td>
                            <div class="lead">
                              <div class="lead-image">
                                <img src="../assets/images/lead/lead-5.png" alt="" />
                              </div>
                              <div class="lead-text">
                               New User 11
                              </div>
                            </div>
                          </td>
                          <td>
                             <EMAIL>
                          </td>
                          <td>
                            (111)222 333454
                          </td>
                          <td>
                            New Agency 11
                          </td>
                          <td>
                           <div class="action">
                   
                              <button class="" data-bs-toggle="modal" data-bs-target="#editStudentModal">
                                <i class="lni lni-eye" style="color:#ffffff;margin-left: 15px;"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                        <!-- end table row -->
						
                      </tbody>
					  
                    </table>
                    <!-- end table -->
                  </div>
                  <!-- Pagination controls -->
                  <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                      <span id="page-info"></span>
                    </div>
                    <nav aria-label="Table pagination">
                      <ul class="pagination" id="pagination-controls">
                        <!-- Pagination items will be generated here by JavaScript -->
                      </ul>
                    </nav>
                  </div>
				  
				  
				  
				  
				  
				  
				  
				  
				  
				  
		
							  
					<!-- View Student Attendance Modal -->
			<div class="modal fade" id="editStudentModal" tabindex="-1" aria-labezlledby="editStudentModalLabel" aria-hidden="true">
			  <div class="modal-dialog modal-xl"><!-- Wider for table layout -->
				<div class="modal-content">
				  <div class="modal-header">
					<h5 class="modal-title" id="editStudentModalLabel">Student Attendance</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				  </div>
				  <div class="modal-body">
					
					<!-- Student Info (Dynamically filled if needed) -->
					

					<!-- Date Range Filter -->
					 <!-- <div class="row mb-3 align-items-end">
					  <div class="col-md-4">
						<label for="startDate" class="form-label">Start Date</label>
						<input type="date" id="startDate" class="form-control">
					  </div>
					  <div class="col-md-4">
						<label for="endDate" class="form-label">End Date</label>
						<input type="date" id="endDate" class="form-control">
					  </div>
					  <div class="col-md-2">
						<button type="button" class="btn btn-primary w-100" id="viewActivityBtn">View</button>
					  </div>
					</div>-->
					
					
					
											
								  <div class="cards-styles">
						  <div class="row">

							<!-- ========= CARD 1: Profile ========= -->
							<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
							  <div class="card-style-1 mb-30">
								<div class="card-meta d-flex align-items-center gap-3">
								  <div style="width: 100px; height: 100px;">
									<img src="../assets/images/lead/lead-1.png" alt="Profile"
									  style="width: 100%; height: 100%; object-fit: contain;" />
								  </div>
								  <div class="text">
									<p class="text-sm text-medium" style="font-size: 23px; margin: 0;">
									  JIMWEL MANGUIAT
									</p>
									<p class="text-sm text-medium" style="font-size: 15px; margin: 0;">
									  BSIT
									</p>
								  </div>
								</div>

								<div class="card-image mt-3">
								  <a href="#0">
								  <!--   <img src="assets/images/cards/card-style-1/card-1.jpg" alt="Card image" class="img-fluid" />-->
								  </a>
								</div>
							  </div>
							</div>

							<!-- ========= CARD 2: Date Range Filter ========= -->
						   <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
						  <div class="card-style-1 mb-30 p-4">
							<div class="row align-items-end">
							  <div class="col-md-4">
								<label for="startDate" class="form-label">Start Date</label>
								<input type="date" id="startDate" class="form-control">
							  </div>
							  <div class="col-md-4">
								<label for="endDate" class="form-label">End Date</label>
								<input type="date" id="endDate" class="form-control">
							  </div>
							  <div class="col-md-2">
								<label class="d-block">&nbsp;</label>
								<button type="button" class="btn btn-primary w-100" id="viewActivityBtn">View</button>
							  </div>
							</div>
						  </div>
						</div>




						  </div>
						</div>











                  <div class="table-wrapper table-responsive">
                    <table class="table">
                      <thead>
                        <tr>
                          <th class="lead-info" style=>
                            <h6>Date</h6>
                          </th>
                          <th class="lead-email">
                            <h6>Time In</h6>
                          </th>
                          <th class="lead-phone">
                            <h6>Time Out</h6>
                          </th>
                          <th class="lead-company">
                            <h6>Event/Active</h6>
                          </th>

                        </tr>
                        <!-- end table row-->
                      </thead>
                      <tbody>
                        <tr>
                          <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <!-- <img src="../assets/images/lead/lead-1.png" alt="" /> -->
                              </div>
                              <div class="lead-text">
                                <p>06/01/2025</p>
                              </div>
                            </div>
                          </td>
                          <td class="min-width">
                            <p><a href="#0">7:30 Am</a></p>
                          </td>
                          <td class="min-width">
                            <p>12:00 Pm</p>
                          </td>
                          <td class="min-width">
                            <p>Class</p>
                          </td>
                          <td>
                       
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <!-- <img src="../assets/images/lead/lead-1.png" alt="" /> -->
                              </div>
                              <div class="lead-text">
                                <p>06/01/2025</p>
                              </div>
                            </div>
                          </td>
                          <td class="min-width">
                            <p><a href="#0">7:30 Am</a></p>
                          </td>
                          <td class="min-width">
                            <p>12:00 Pm</p>
                          </td>
                          <td class="min-width">
                            <p>EPC MASS</p>
                          </td>
                          <td>
                       
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <!-- <img src="../assets/images/lead/lead-1.png" alt="" /> -->
                              </div>
                              <div class="lead-text">
                                <p>06/01/2025</p>
                              </div>
                            </div>
                          </td>
                          <td class="min-width">
                            <p><a href="#0">7:30 Am</a></p>
                          </td>
                          <td class="min-width">
                            <p>12:00 Pm</p>
                          </td>
                          <td class="min-width">
                            <p>MORNING ASSEMBLY</p>
                          </td>
                          <td>
                       
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <!-- <img src="../assets/images/lead/lead-1.png" alt="" /> -->
                              </div>
                              <div class="lead-text">
                                <p>06/01/2025</p>
                              </div>
                            </div>
                          </td>
                          <td class="min-width">
                            <p><a href="#0">7:30 Am</a></p>
                          </td>
                          <td class="min-width">
                            <p>12:00 Pm</p>
                          </td>
                          <td class="min-width">
                            <p>Class</p>
                          </td>
                          <td>
                       
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <!-- <img src="../assets/images/lead/lead-1.png" alt="" /> -->
                              </div>
                              <div class="lead-text">
                                <p>06/01/2025</p>
                              </div>
                            </div>
                          </td>
                          <td class="min-width">
                            <p><a href="#0">7:30 Am</a></p>
                          </td>
                          <td class="min-width">
                            <p>12:00 Pm</p>
                          </td>
                          <td class="min-width">
                            <p>DEPARTMENTAL MASS</p>
                          </td>
                          <td>
                       
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <!-- <img src="../assets/images/lead/lead-1.png" alt="" /> -->
                              </div>
                              <div class="lead-text">
                                <p>06/01/2025</p>
                              </div>
                            </div>
                          </td>
                          <td class="min-width">
                            <p><a href="#0">7:30 Am</a></p>
                          </td>
                          <td class="min-width">
                            <p>12:00 Pm</p>
                          </td>
                          <td class="min-width">
                            <p>EPC MASS</p>
                          </td>
                          <td>
                       
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <!-- <img src="../assets/images/lead/lead-1.png" alt="" /> -->
                              </div>
                              <div class="lead-text">
                                <p>06/01/2025</p>
                              </div>
                            </div>
                          </td>
                          <td class="min-width">
                            <p><a href="#0">7:30 Am</a></p>
                          </td>
                          <td class="min-width">
                            <p>12:00 Pm</p>
                          </td>
                          <td class="min-width">
                            <p>RETREAT</p>
                          </td>
                          <td>
                       
                          </td>
                        </tr>
                        <!-- end table row -->
                        <tr>
                          <td class="min-width">
                            <div class="lead">
                              <div class="lead-image">
                                <!-- <img src="../assets/images/lead/lead-1.png" alt="" /> -->
                              </div>
                              <div class="lead-text">
                                <p>06/01/2025</p>
                              </div>
                            </div>
                          </td>
                          <td class="min-width">
                            <p><a href="#0">7:30 Am</a></p>
                          </td>
                          <td class="min-width">
                            <p>12:00 Pm</p>
                          </td>
                          <td class="min-width">
                            <p>Class</p>
                          </td>
                          <td>
                       
                          </td>
                        </tr>
                   
                        <!-- end table row -->
						
                      </tbody>
					  
                    </table>
                    <!-- end table -->
                  </div>

					
					

					<!-- Pagination Controls -->
					<nav class="mt-3 d-flex justify-content-end">
					  <ul class="pagination mb-0">
						<li class="page-item">
						  <button class="page-link" id="prevPage">Previous</button>
						</li>
						<li class="page-item">
						  <button class="page-link" id="nextPage">Next</button>
						</li>
					  </ul>
					</nav>

				  </div>
				  <div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
				  </div>
				</div>
			  </div>
			</div>







  


                </div>
                <!-- end card -->
              </div>
              <!-- end col -->
            </div>
            <!-- end row -->
          </div>
          <!-- ========== tables-wrapper end ========== -->
            </div>
            <!-- end row -->
          </div>
        </div>
    </section>
    <?php include '../assets/common/footer.php'; ?>
  </main>
  <?php include '../assets/common/scripts.php'; ?>
  
  
    <!-- Pagination Script STUDENT ATTENDANCE START -->
<script>
  document.addEventListener('DOMContentLoaded', function () {
    const rowsPerPage = 3;
    let currentPage = 1;

    const getRows = () => Array.from(document.querySelectorAll('#activityTableBody tr'));

    const renderTable = () => {
      const rows = getRows();
      const start = (currentPage - 1) * rowsPerPage;
      const end = start + rowsPerPage;

      rows.forEach((row, index) => {
        row.style.display = index >= start && index < end ? '' : 'none';
      });

      document.getElementById('prevPage').disabled = currentPage === 1;
      document.getElementById('nextPage').disabled = end >= rows.length;
    };

    document.getElementById('prevPage').addEventListener('click', () => {
      if (currentPage > 1) {
        currentPage--;
        renderTable();
      }
    });

    document.getElementById('nextPage').addEventListener('click', () => {
      const rows = getRows();
      if ((currentPage * rowsPerPage) < rows.length) {
        currentPage++;
        renderTable();
      }
    });

    renderTable(); // Run initially
  });
</script>
    <!-- Pagination Script STUDENT ATTENDANCE END -->
	
	
  
  <!-- ========== STUDENT ATTENDANCE MODAL SCRIPT ========== -->
  
  <script>
document.addEventListener('DOMContentLoaded', function() {
    const viewMenusBtn = document.getElementById('viewMenusBtn');
    const userRoleDropdown = document.getElementById('userRoleDropdown');
    const menuForm = document.getElementById('menuForm');
    const adminMenus = document.getElementById('adminMenus');
    const staffMenus = document.getElementById('staffMenus');

    function setupMainMenuListeners() {
        document.querySelectorAll('.main-menu').forEach(mainMenu => {
            mainMenu.addEventListener('change', function() {
                const subMenus = this.closest('.menu-section').querySelectorAll('.sub-menu');
                subMenus.forEach(subMenu => {
                    subMenu.checked = this.checked;
                });
            });
        });

        
        document.querySelectorAll('.menu-section').forEach(section => {
            const subMenus = section.querySelectorAll('.sub-menu');
            const mainMenu = section.querySelector('.main-menu');

            subMenus.forEach(subMenu => {
                subMenu.addEventListener('change', function() {
                    const allSubMenus = Array.from(subMenus);
                    mainMenu.checked = allSubMenus.every(sm => sm.checked);
                });
            });
        });
    }

    viewMenusBtn.addEventListener('click', function() {
        const selectedRole = userRoleDropdown.value;
        
       
        menuForm.style.display = 'block';
        
      
        if (selectedRole === 'admin') {
            adminMenus.style.display = 'block';
            staffMenus.style.display = 'none';
        } else {
            adminMenus.style.display = 'none';
            staffMenus.style.display = 'block';
        }

        setupMainMenuListeners();
    });

    const viewMenuModal = document.getElementById('viewMenuModal');
    viewMenuModal.addEventListener('hidden.bs.modal', function() {
        menuForm.style.display = 'none';
        adminMenus.style.display = 'none';
        staffMenus.style.display = 'none';
    });
});
</script>
  
  <!-- ========== STUDENT ATTENDANCE MODAL END SCRIPT ========== -->




  
  <!-- ========== PREVIOUS NEXT SCRIPT START ========== -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const tableBody = document.querySelector('.table tbody');
      if (!tableBody) {
        console.error('Table body not found!');
        return;
      }
      // Convert NodeList to Array to ensure all array methods are available, though querySelectorAll often returns an iterable that works with forEach.
      const rows = Array.from(tableBody.querySelectorAll('tr'));
      const rowsPerPage = 10;
      let currentPage = 0; // 0-indexed
      const paginationControlsContainer = document.getElementById('pagination-controls');
      const pageInfoSpan = document.getElementById('page-info');

      function displayPage(page) {
        currentPage = page;
        const start = page * rowsPerPage;
        const end = start + rowsPerPage;

        rows.forEach((row, index) => {
          row.style.display = (index >= start && index < end) ? '' : 'none';
        });
        renderPaginationControls();
      }

      function renderPaginationControls() {
        if (!paginationControlsContainer || !pageInfoSpan) {
            console.error('Pagination UI elements not found');
            return;
        }
        paginationControlsContainer.innerHTML = ''; // Clear existing controls
        
        const totalRows = rows.length;
        const totalPages = Math.ceil(totalRows / rowsPerPage);

        // Update page info text
        if (totalRows === 0) {
            pageInfoSpan.textContent = 'No entries to display.';
            paginationControlsContainer.style.display = 'none';
            return;
        } else {
            paginationControlsContainer.style.display = 'flex'; // Show if there are pages
            const startEntry = currentPage * rowsPerPage + 1;
            const endEntry = Math.min((currentPage + 1) * rowsPerPage, totalRows);
            pageInfoSpan.textContent = `Showing ${startEntry} to ${endEntry} of ${totalRows} entries (Page ${currentPage + 1} of ${totalPages})`;
        }
        
        if (totalPages <= 1) { // No need for pagination controls if only one page or less
            paginationControlsContainer.style.display = 'none';
            return;
        }


        // Previous Button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 0 ? 'disabled' : ''}`;
        const prevLink = document.createElement('a');
        prevLink.className = 'page-link';
        prevLink.href = '#';
        prevLink.textContent = 'Previous';
        prevLink.setAttribute('aria-label', 'Previous');
        if (currentPage > 0) {
            prevLink.addEventListener('click', (e) => {
              e.preventDefault();
              displayPage(currentPage - 1);
            });
        } else {
            prevLink.setAttribute('tabindex', '-1'); 
            prevLink.style.pointerEvents = 'none'; 
        }
        prevLi.appendChild(prevLink);
        paginationControlsContainer.appendChild(prevLi);

        
        for (let i = 0; i < totalPages; i++) {
          const pageLi = document.createElement('li');
          pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
          const pageLink = document.createElement('a');
          pageLink.className = 'page-link';
          pageLink.href = '#';
          pageLink.textContent = i + 1;
          if (i === currentPage) {
            pageLink.setAttribute('aria-current', 'page');
          }
          pageLink.addEventListener('click', (e) => {
            e.preventDefault();
            if (i !== currentPage) { 
                displayPage(i);
            }
          });
          pageLi.appendChild(pageLink);
          paginationControlsContainer.appendChild(pageLi);
        }

        
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages - 1 ? 'disabled' : ''}`;
        const nextLink = document.createElement('a');
        nextLink.className = 'page-link';
        nextLink.href = '#';
        nextLink.textContent = 'Next';
        nextLink.setAttribute('aria-label', 'Next');
         if (currentPage < totalPages - 1) {
            nextLink.addEventListener('click', (e) => {
              e.preventDefault();
              displayPage(currentPage + 1);
            });
        } else {
            nextLink.setAttribute('tabindex', '-1');
            nextLink.style.pointerEvents = 'none';
        }
        nextLi.appendChild(nextLink);
        paginationControlsContainer.appendChild(nextLi);
      }

      // Initial load
      if (rows.length > 0) {
          displayPage(currentPage); 
      } else {
          renderPaginationControls(); 
      }
    });
  </script>
   <!-- ========== PREVIOUS NEXT SCRIPT END ========== -->
   
   
   
      <!-- ========== DATE FILTER SCRIPT START ========== -->
	  
   <script>
  const startDateInput = document.getElementById('startDate');
  const endDateInput = document.getElementById('endDate');

  startDateInput.addEventListener('change', function () {
    endDateInput.value = this.value;
  });
</script>
      <!-- ========== DATE FILTER SCRIPT END ========== -->
   
   
</body>
</html>