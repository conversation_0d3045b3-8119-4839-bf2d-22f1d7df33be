(function () {
  /* ========= Preloader ======== */
  const preloader = document.querySelectorAll('#preloader')

  window.addEventListener('load', function () {
    if (preloader.length) {
      this.document.getElementById('preloader').style.display = 'none'
    }
  })

  /* ========= Add Box Shadow in Header on Scroll ======== */
  document.addEventListener('DOMContentLoaded', () => {
    const header = document.querySelector('.header');
    if (header) {
      window.addEventListener('scroll', function () {
        if (window.scrollY > 0) {
          header.style.boxShadow = '0px 0px 30px 0px rgba(200, 208, 216, 0.30)';
        } else {
          header.style.boxShadow = 'none';
        }
      });
    }
  });

  /* ========= sidebar toggle ======== */
  document.addEventListener('DOMContentLoaded', () => {
    const sidebarNavWrapper = document.querySelector(".sidebar-nav-wrapper");
    const mainWrapper = document.querySelector(".main-wrapper");
    const menuToggleButton = document.querySelector("#menu-toggle");
    const menuToggleButtonIcon = document.querySelector("#menu-toggle i");
    const overlay = document.querySelector(".overlay");

    if (menuToggleButton) {
      menuToggleButton.addEventListener("click", () => {
    sidebarNavWrapper.classList.toggle("active");
    overlay.classList.add("active");
    mainWrapper.classList.toggle("active");

    if (document.body.clientWidth > 1200) {
      if (menuToggleButtonIcon.classList.contains("lni-chevron-left")) {
        menuToggleButtonIcon.classList.remove("lni-chevron-left");
        menuToggleButtonIcon.classList.add("lni-menu");
      } else {
        menuToggleButtonIcon.classList.remove("lni-menu");
        menuToggleButtonIcon.classList.add("lni-chevron-left");
      }
    } else {
      if (menuToggleButtonIcon.classList.contains("lni-chevron-left")) {
        menuToggleButtonIcon.classList.remove("lni-chevron-left");
        menuToggleButtonIcon.classList.add("lni-menu");
      }
    }
      });
    }

    if (overlay) {
      overlay.addEventListener("click", () => {
        sidebarNavWrapper.classList.remove("active");
        overlay.classList.remove("active");
        mainWrapper.classList.remove("active");
      });
    }
  });
})();
/* JavaScript to handle long names */
document.addEventListener('DOMContentLoaded', function() {
  // Handle profile names
  const profileNames = document.querySelectorAll('.profile-info .info h6');
  profileNames.forEach(name => {
    if (name.textContent.length > 12) {
      name.classList.add('long-name');
    }
  });

  // Handle college title
  const collegeTitle = document.querySelector('.college-title h1');
  if (collegeTitle) {
    if (collegeTitle.textContent.length > 12) {
      collegeTitle.classList.add('long-name');
    }
  }
});
document.addEventListener('DOMContentLoaded', function() {
  // Handle all profile names in dropdowns and author sections
  const usernames = document.querySelectorAll('.dropdown-header .user-info h4, .author-info .content h4');
  usernames.forEach(name => {
    const length = name.textContent.trim().length;
    if (length > 20) {
      name.setAttribute('data-length', 'very-long');
    } else if (length > 12) {
      name.setAttribute('data-length', 'long');
    }
  });

  // Dark Mode Functionality
  function toggleDarkMode() {
    const body = document.body;
    const isDark = !body.classList.contains('dark-mode');
    
    // Toggle dark mode class
    body.classList.toggle('dark-mode');
    localStorage.setItem('darkMode', isDark);
    
    // Update dock button
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
      const themeIcon = themeToggle.querySelector('i');
      themeIcon.className = isDark ? 'lni lni-night' : 'lni lni-sun';
    }
    
    // Update profile dropdown button
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
      const darkModeIcon = darkModeToggle.querySelector('i');
      const darkModeStatus = darkModeToggle.querySelector('.dark-mode-status');
      darkModeIcon.className = isDark ? 'lni lni-night' : 'lni lni-sun';
      if (darkModeStatus) {
        darkModeStatus.textContent = isDark ? '(On)' : '(Off)';
      }
    }
  }

  // Initialize dark mode
  const isDarkMode = localStorage.getItem('darkMode') === 'true';
  if (isDarkMode) {
    document.body.classList.add('dark-mode');
    
    // Update dock button initial state
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
      themeToggle.querySelector('i').className = 'lni lni-night';
    }
    
    // Update profile button initial state
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
      darkModeToggle.querySelector('i').className = 'lni lni-night';
      const darkModeStatus = darkModeToggle.querySelector('.dark-mode-status');
      if (darkModeStatus) {
        darkModeStatus.textContent = '(On)';
      }
    }
  }

  // Add theme toggle event listeners
  const themeToggle = document.getElementById('themeToggle');
  if (themeToggle) {
    themeToggle.addEventListener('click', function(e) {
      e.preventDefault();
      toggleDarkMode();
    });
  }
  
  const darkModeToggle = document.getElementById('darkModeToggle');
  if (darkModeToggle) {
    darkModeToggle.addEventListener('click', function(e) {
      e.preventDefault();
      toggleDarkMode();
    });
  }

  // Handle floating dock visibility
  function updateDockVisibility() {
    const dock = document.querySelector('.floating-dock');
    if (!dock) return;
    
    const items = dock.querySelectorAll('*');
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
      dock.classList.add('show');
      items.forEach(item => {
        item.style.display = '';
        item.style.visibility = '';
      });
    } else {
      dock.classList.remove('show');
      items.forEach(item => {
        item.style.display = 'none';
        item.style.visibility = 'hidden';
      });
    }
  }
  
  // Initial visibility check
  if (document.querySelector('.floating-dock')) {
    updateDockVisibility();
  }
  
  // Handle dock menu click on mobile
  const dockMenu = document.getElementById('dockMenu');
  if (dockMenu) {
    dockMenu.addEventListener('click', function(e) {
      if (window.innerWidth <= 768) {
        if (window.innerWidth <= 768) {
            e.preventDefault();
            const sidebarNav = document.querySelector('.sidebar-nav-wrapper');
            const mainWrapper = document.querySelector('.main-wrapper');
            const overlay = document.querySelector('.overlay');
            
            sidebarNav.classList.toggle('active');
            overlay.classList.toggle('active');
            mainWrapper.classList.toggle('active');
        }
      }
    });
  }

  // Update dock visibility on resize
  let resizeTimer;
  window.addEventListener('resize', function() {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(updateDockVisibility, 250);
  });
});
