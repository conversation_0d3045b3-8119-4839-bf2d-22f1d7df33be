<?php
    class MENUS
    {
    ///////////////////YZAI/////////////////
	function get_allmenu($db) 
	{
        $data = array();
        $stmt1 = $db->prepare("SELECT * FROM menu
		ORDER BY menuid ASC");
        $stmt1->execute();
        for($i=1; $i<=$stmt1->rowCount(); $i++)
            $data[] = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $data;
    }
	
	function get_submenumenuid($db,$submenuid) 
	{
        $data = array();
        $stmt1 = $db->prepare("SELECT * FROM submenu WHERE submenuid=?");
        $stmt1->execute(array($submenuid));
        $row = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $row;
    }




    
     function get_userpassport($db, $uaid) 
{
    $data = array();
    $stmt1 = $db->prepare("SELECT * FROM userpassport WHERE uaid=? AND up_status='Active' ORDER BY menuid ASC, submenuid ASC");
    $stmt1->execute(array($uaid));
    for ($i = 0; $i < $stmt1->rowCount(); $i++)
        $data[] = $stmt1->fetch(PDO::FETCH_ASSOC);
    return $data;
}

    function get_allsubmenu($db) 
	{
        $data = array();
        $stmt1 = $db->prepare("SELECT * FROM submenu
		ORDER BY menuid ASC");
        $stmt1->execute();
        for($i=1; $i<=$stmt1->rowCount(); $i++)
            $data[] = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function delete_userpassport($db, $uaid) {
        $stmt = $db->prepare("DELETE FROM userpassport WHERE uaid = ?");
        $stmt->execute(array($uaid));
        return $stmt->rowCount();
    }

    function insert_userpassport($db, $uaid, $modid, $menuid, $submenuid) {
        $stmt = $db->prepare("INSERT INTO userpassport (uaid, modid, menuid, submenuid, up_status) VALUES (?, ?, ?, ?, 'Active')");
        $stmt->execute(array($uaid, $modid, $menuid, $submenuid));
        return $db->lastInsertId();
    }

    function check_user_main_menu_exists($db, $uaid, $menuid) {
        $stmt = $db->prepare("SELECT COUNT(*) FROM userpassport WHERE uaid = ? AND menuid = ? AND (submenuid = 0 OR submenuid IS NULL)");
        $stmt->execute([$uaid, $menuid]);
        return $stmt->fetchColumn() > 0;
    }
}
?>