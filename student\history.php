<?php
session_start();
require '../core/dbcon.ini';
require 'query/students.qry';
require 'classes/os_browser.php';

// Initialize class
$students = new STUDENTS();

// Get IP address
$ip_address = getenv("REMOTE_ADDR");

try {
    $student_data = $students->check_device_registration($db1, $ip_address);

    if (!$student_data) {
        header("Location: attendance.php");
        exit();
    }

    $profile = $student_data;
    $profile['name'] = $profile['first_name'] . ' ' . $profile['last_name'];
    $profile['image'] = "assets/uploads/" . $profile['image'];

    // Pagination
    $limit = 10;
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $offset = ($page - 1) * $limit;

    // Get total records and calculate pages
    $total_records = $students->get_total_attendance_records($db1, $profile['student_no']);
    $total_pages = ceil($total_records / $limit);

    // Get attendance records
    $attendance = $students->get_attendance_records($db1, $profile['student_no'], $limit, $offset);

} catch(PDOException $e) {
    error_log("Error in history.php: " . $e->getMessage());
    header("Location: attendance.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <title>Don Bosco College - Attendance History</title>
    <link rel="stylesheet" href="css/history.css">

</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Attendance History</h1>
            <p style="color: var(--text-secondary); font-weight: 600; font-size: 16px; margin: 8px 0 0 0;">
                <?php echo $profile['name']; ?>
            </p>
        </div>

        <?php if (!empty($attendance)): ?>
            <table>
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Time In</th>
                        <th>Time Out</th>
                        <th>Duration</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($attendance as $row): ?>
                        <tr>
                            <td><?php echo date('M j, Y', strtotime($row['time_in'])); ?></td>
                            <td><?php echo date('h:i A', strtotime($row['time_in'])); ?></td>
                            <td>
                                <?php echo $row['time_out'] ? 
                                    date('h:i A', strtotime($row['time_out'])) : 
                                    '--:--'; ?>
                            </td>
                            <td>
                                <?php if ($row['time_out']): 
                                    $diff = strtotime($row['time_out']) - strtotime($row['time_in']);
                                    echo gmdate('H\h i\m', $diff);
                                else: 
                                    echo '--:--';
                                endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($page > 1): ?>
                        <a href="?page=<?php echo $page - 1; ?>">&laquo; Previous</a>
                    <?php endif; ?>

                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <a href="?page=<?php echo $i; ?>" 
                           class="<?php echo $i === $page ? 'active' : ''; ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($page < $total_pages): ?>
                        <a href="?page=<?php echo $page + 1; ?>">Next &raquo;</a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="no-data">
                <p>No attendance records found.</p>
            </div>
        <?php endif; ?>

        <a href="attendance.php" class="back-btn">← Back to Attendance</a>
    </div>
</body>
</html>
<?php
$db1 = null; // Close connection
?>
