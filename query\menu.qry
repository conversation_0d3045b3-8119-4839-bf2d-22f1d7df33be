<?php
class MENU
{
    function get_menu($db) {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM menu ORDER BY menuid ASC");
        $stmt->execute();
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }
    
    function get_validmenu($db, $uaid, $menuid) {
        $stmt = $db->prepare("SELECT * FROM userpassport WHERE uaid=? AND menuid=?");
        $stmt->execute(array($uaid, $menuid));
        return $stmt->rowCount();
    }
    
    function get_user_menus($db, $userid) {
        $data = array();
        
        $stmt = $db->prepare("SELECT DISTINCT menu.menuid, menu.menutitle, menu.icon 
            FROM menu 
            JOIN userpassport ON menu.menuid = userpassport.menuid
            WHERE userpassport.uaid = ?
            ORDER BY menu.menuid ASC");
        $stmt->execute(array($userid));
        
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);         
        return $data;
    }

    function get_submenu($db, $menuid, $userid) {
        $data = array();
        $stmt = $db->prepare("SELECT submenu.submenuid, submenu.submenutitle, 
            submenu.submenulink as link, submenu.icon
            FROM submenu
            JOIN userpassport ON submenu.submenuid = userpassport.submenuid
            WHERE userpassport.uaid = ? AND submenu.menuid = ?
            ORDER BY submenu.submenuid ASC");
        $stmt->execute(array($userid, $menuid));
        
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);          
        return $data;
    }
	
	////////////////////YZAI//////////////////
	
	function get_menumenuid($db,$menuid) 
	{
        $row = array();
        $stmt1 = $db->prepare("SELECT * FROM menu WHERE menuid=?
		ORDER BY menuid ASC");
        $stmt1->execute(array($menuid));
        $row = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $row;
    }
	
	///////////////////YZAI/////////////////
	function get_allmenu($db) 
	{
        $data = array();
        $stmt1 = $db->prepare("SELECT * FROM menu
		ORDER BY menuid ASC");
        $stmt1->execute();
        for($i=1; $i<=$stmt1->rowCount(); $i++)
            $data[] = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $data;
    }
	
	function get_submenumenuid($db,$submenuid) 
	{
        $data = array();
        $stmt1 = $db->prepare("SELECT * FROM submenu WHERE submenuid=?");
        $stmt1->execute(array($submenuid));
        $row = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $row;
    }

}
?>
