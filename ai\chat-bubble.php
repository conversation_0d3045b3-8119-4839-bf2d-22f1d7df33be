<!-- Floating Chat Bubble -->
<div class="chat-bubble-container">
    <div class="chat-bubble-toggle">
        <i class="lni lni-comments"></i>
    </div>
</div>

<!-- Sliding Chat Panel -->
<div class="chat-panel rounded-panel">
    <div class="chat-panel-header">
        <span>
            <img src="../assets/images/logo/dbc.png" alt="Don Bosco Logo" width="30" height="30" class="align-middle">
            <h5 class="d-inline align-middle">Bosco AI</h5>
        </span>
        <div class="chat-panel-controls">
            <button class="chat-expand-btn input-action-btn" title="Expand/Collapse chat">
                <i class="lni lni-frame-expand"></i>
                <i class="lni lni-frame-contract"></i>
            </button>
            <button class="chat-clear-btn input-action-btn" title="Clear chat history">
                <i class="lni lni-trash-can"></i>
            </button>
            <button class="chat-close-btn input-action-btn">
                <i class="lni lni-close"></i>
            </button>
        </div>
    </div>
    <div class="chat-panel-messages">
        <!-- Messages will be appended here -->
    </div>
    <div class="chat-panel-input">
        <div class="input-group modern-input">
            <input type="text" class="form-control" id="chat-bubble-input" placeholder="Send a message..." autocomplete="off">
            <div class="input-actions">
                <label class="btn input-action-btn" for="file-upload">
                    <i class="lni lni-upload"></i>
                    <input type="file" id="file-upload" accept=".pdf,image/*" style="display:none">
                </label>
                <button class="btn input-action-btn" type="button" id="chat-bubble-send">
                    <i class="lni lni-arrow-right-circle"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Chat Bubble Scripts -->
<link rel="stylesheet" href="../assets/css/main.css">
<link rel="stylesheet" href="../assets/css/chat-bubble.css">
<script src="../assets/js/chat-bubble.js"></script>
