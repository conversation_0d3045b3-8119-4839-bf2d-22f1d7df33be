<?php
class ACCESS
{
    function get_useraccountscount($db, $uname, $pword)
    {
        $stmt1 = $db->prepare("SELECT * FROM `useraccounts` 
        WHERE username=? AND password=?");
        $stmt1->execute(array($uname, $pword));    
        return $stmt1->rowCount();
    }
    
    function get_useraccounts($db, $uname, $pword)
    {
        $stmt1 = $db->prepare("SELECT *, status FROM `useraccounts`
        WHERE username=? AND password=?");
        $stmt1->execute(array($uname, $pword));
        $row = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $row;
    }

    function get_useraccountsuaid($db, $uaid)
    {
        $stmt1 = $db->prepare("SELECT uaid, username, firstname, lastname, password, modid, position, profilePicture, emp_email FROM `useraccounts`
        WHERE uaid=?");
        $stmt1->execute(array($uaid));
        $row = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $row;
    }
    
    //************UPDATE************//
	
    function upd_useraccountsuaid($db, $pword, $uaid)
    {
        $stmt1 = $db->prepare("UPDATE `useraccounts` 
        SET password=? WHERE uaid=?");
        $stmt1->execute(array($pword, $uaid));
    }

    function upd_profile_picture($db, $profile_picture, $uaid) 
    {
        $stmt1 = $db->prepare("UPDATE `useraccounts` 
        SET profilePicture=? WHERE uaid=?");
        $stmt1->execute(array($profile_picture, $uaid));
    }
	
	
	///////////////////YZAI/////////////////
	function get_userpassportmenuid($db,$menuid,$uaid) 
	{
        $data = array();
        $stmt = $db->prepare("SELECT * FROM userpassport WHERE menuid=? and uaid=?");
        $stmt->execute(array($menuid,$uaid));
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

        ///////////////////YZAI/////////////////
    function get_alluseraccounts($db) 
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM useraccounts");
        $stmt->execute();
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }
}
?>
