<?php
session_start();
include_once '../core/dbcon.ini';
include_once '../query/access.qry';
include_once '../includes/session.php';

$accessfnc = new ACCESS();

if (!isset($_SESSION['uaid'])) {
    header("Location: ../login.php");
    exit();
}

$uaid = $_SESSION['uaid'];
if (isset($_POST['updatePicture'])) {
    $response = ['success' => false, 'message' => ''];

    if (isset($_FILES['profileImage']) && $_FILES['profileImage']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['profileImage'];
        $fileName = $file['name'];
        $fileTmpName = $file['tmp_name'];
        $fileSize = $file['size'];
        $fileError = $file['error'];
        $fileType = $file['type'];

        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        $allowed = array('jpg', 'jpeg', 'png', 'gif');

        if (in_array($fileExt, $allowed)) {
            if ($fileError === 0) {
                if ($fileSize < 5000000) {
                    $fileNameNew = uniqid('', true) . "." . $fileExt;
                    $fileDestination = '../assets/images/profile/uploads/' . $fileNameNew;

                    if (move_uploaded_file($fileTmpName, $fileDestination)) {
                        $accessfnc->upd_profile_picture($db1, $fileNameNew, $uaid);
                        $_SESSION['profile_picture'] = $fileNameNew;
                        $response['success'] = true;
                        $response['message'] = 'Profile picture updated successfully.';
                        $response['fileNameNew'] = $fileNameNew;
                    } else {
                        $response['message'] = 'Failed to upload file.';
                    }
                } else {
                    $response['message'] = 'Your file is too large! Max 5MB.';
                }
            } else {
                $response['message'] = 'There was an error uploading your file.';
            }
        } else {
            $response['message'] = 'You cannot upload files of this type. Only JPG, PNG, GIF are allowed.';
        }
    } else {
        $response['message'] = 'No file uploaded or an error occurred.';
    }
    echo json_encode($response);
    exit();
}
if (isset($_POST['updatePassword'])) {
    $response = ['success' => false, 'message' => ''];

    $currentPassword = $_POST['currentPassword'];
    $newPassword = $_POST['newPassword'];
    $confirmPassword = $_POST['confirmPassword'];
    $userData = $accessfnc->get_useraccountsuaid($db1, $uaid);

    if ($userData) {
   
        if ($currentPassword === $userData['password']) {
            if ($newPassword === $confirmPassword) {
                if (!empty($newPassword)) {
                    $accessfnc->upd_useraccountsuaid($db1, $newPassword, $uaid);
                    $response['success'] = true;
                    $response['message'] = 'Password updated successfully.';
                } else {
                    $response['message'] = 'New password cannot be empty.';
                }
            } else {
                $response['message'] = 'New password and confirm password do not match.';
            }
        } else {
            $response['message'] = 'Incorrect current password.';
        }
    } else {
        $response['message'] = 'User not found.';
    }
    echo json_encode($response);
    exit();
}

// Set alert and redirect
$_SESSION['alert'] = ['type' => $type, 'message' => $msg];
header("Location: ../index.php");
?>
