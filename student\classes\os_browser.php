<?php
$ipaddress = getenv("REMOTE_ADDR");
$user_agent = $_SERVER['HTTP_USER_AGENT'];

function getOS() { 
    global $user_agent;
    $os_platform = "Unknown OS Platform";
    $os_array = array(
        '/windows nt 10/i'      =>  'Windows 10',
        '/windows nt 6.3/i'     =>  'Windows 8.1',
        '/windows nt 6.2/i'     =>  'Windows 8',
        '/windows nt 6.1/i'     =>  'Windows 7',
        '/windows nt 6.0/i'     =>  'Windows Vista',
        '/windows nt 5.2/i'     =>  'Windows Server 2003/XP x64',
        '/windows nt 5.1/i'     =>  'Windows XP',
        '/windows xp/i'         =>  'Windows XP',
        '/windows nt 5.0/i'     =>  'Windows 2000',
        '/windows me/i'         =>  'Windows ME',
        '/win98/i'              =>  'Windows 98',
        '/win95/i'              =>  'Windows 95',
        '/win16/i'              =>  'Windows 3.11',
        
        // Mac OS detailed detection
        '/mac os x 14/i'        =>  'macOS Sequoia',
        '/mac os x 13/i'        =>  'macOS Ventura',
        '/mac os x 12/i'        =>  'macOS Monterey',
        '/mac os x 11/i'        =>  'macOS Big Sur',
        '/mac os x 10.15/i'     =>  'macOS Catalina',
        '/mac os x 10.14/i'     =>  'macOS Mojave',
        '/mac os x 10.13/i'     =>  'macOS High Sierra',
        '/mac os x 10.12/i'     =>  'macOS Sierra',
        '/mac os x 10.1[0-1]/i' =>  'Mac OS X Lion',
        '/mac os x 10/i'        =>  'Mac OS X',
        '/mac_powerpc/i'        =>  'Mac OS 9',
        
        // Detailed iOS detection
        '/iphone os 17/i'       =>  'iOS 17 (iPhone 15/14/13/12 Series)',
        '/iphone os 16/i'       =>  'iOS 16 (iPhone 14/13/12/11 Series)',
        '/iphone os 15/i'       =>  'iOS 15 (iPhone 13/12/11 Series)',
        '/iphone os 14/i'       =>  'iOS 14 (iPhone 12/11/X Series)',
        '/iphone os 13/i'       =>  'iOS 13 (iPhone 11/XS/XR Series)',
        '/iphone os 12/i'       =>  'iOS 12 (iPhone XS/XR/X/8/7 Series)',
        '/iphone os 11/i'       =>  'iOS 11 (iPhone X/8/7 Series)',
        '/iphone os 10/i'       =>  'iOS 10 (iPhone 7/6S Series)',
        '/iphone os 9/i'        =>  'iOS 9 (iPhone 6S/6 Series)',
        '/iphone os/i'          =>  'iOS (iPhone)',
        
        // iPad detailed detection
        '/ipad.*os 17/i'        =>  'iPadOS 17 (iPad Pro/Air/Mini Series)',
        '/ipad.*os 16/i'        =>  'iPadOS 16 (iPad Pro/Air/Mini Series)',
        '/ipad.*os 15/i'        =>  'iPadOS 15 (iPad Pro/Air/Mini Series)',
        '/ipad.*os 14/i'        =>  'iPadOS 14 (iPad Pro/Air/Mini Series)',
        '/ipad.*os 13/i'        =>  'iPadOS 13 (iPad Pro/Air/Mini Series)',
        '/ipad.*os 12/i'        =>  'iOS 12 (iPad Pro/Air/Mini Series)',
        '/ipad.*os/i'           =>  'iOS (iPad)',
        
        // Android detailed detection
        '/android 14/i'         =>  'Android 14 (Pixel 8/Samsung S24 Series)',
        '/android 13/i'         =>  'Android 13 (Pixel 7/Samsung S23 Series)',
        '/android 12/i'         =>  'Android 12 (Pixel 6/Samsung S22 Series)',
        '/android 11/i'         =>  'Android 11 (Pixel 5/Samsung S21 Series)',
        '/android 10/i'         =>  'Android 10 (Pixel 4/Samsung S20 Series)',
        '/android 9/i'          =>  'Android Pie (Pixel 3/Samsung S10 Series)',
        '/android 8/i'          =>  'Android Oreo (Pixel 2/Samsung S9 Series)',
        '/android 7/i'          =>  'Android Nougat (Pixel/Samsung S8 Series)',
        '/android 6/i'          =>  'Android Marshmallow (Samsung S7 Series)',
        '/android 5/i'          =>  'Android Lollipop (Samsung S6 Series)',
        '/android/i'            =>  'Android',
        
        // Specific Samsung device detection
        '/SM-S918/i'            =>  'Samsung Galaxy S23 Ultra',
        '/SM-S916/i'            =>  'Samsung Galaxy S23+',
        '/SM-S911/i'            =>  'Samsung Galaxy S23',
        '/SM-S908/i'            =>  'Samsung Galaxy S22 Ultra',
        '/SM-S906/i'            =>  'Samsung Galaxy S22+',
        '/SM-S901/i'            =>  'Samsung Galaxy S22',
        '/SM-G998/i'            =>  'Samsung Galaxy S21 Ultra',
        '/SM-G996/i'            =>  'Samsung Galaxy S21+',
        '/SM-G991/i'            =>  'Samsung Galaxy S21',
        '/SM-G988/i'            =>  'Samsung Galaxy S20 Ultra',
        '/SM-G986/i'            =>  'Samsung Galaxy S20+',
        '/SM-G981/i'            =>  'Samsung Galaxy S20',
        '/SM-G975/i'            =>  'Samsung Galaxy S10+',
        '/SM-G973/i'            =>  'Samsung Galaxy S10',
        '/SM-G970/i'            =>  'Samsung Galaxy S10e',
        
        // Google Pixel detection
        '/Pixel 8 Pro/i'        =>  'Google Pixel 8 Pro',
        '/Pixel 8/i'            =>  'Google Pixel 8',
        '/Pixel 7 Pro/i'        =>  'Google Pixel 7 Pro',
        '/Pixel 7/i'            =>  'Google Pixel 7',
        '/Pixel 6 Pro/i'        =>  'Google Pixel 6 Pro',
        '/Pixel 6/i'            =>  'Google Pixel 6',
        '/Pixel 5/i'            =>  'Google Pixel 5',
        '/Pixel 4/i'            =>  'Google Pixel 4',
        '/Pixel 3/i'            =>  'Google Pixel 3',
        
        // Other smartphone brands
        '/OnePlus 11/i'         =>  'OnePlus 11',
        '/OnePlus 10/i'         =>  'OnePlus 10',
        '/OnePlus 9/i'          =>  'OnePlus 9',
        '/ONEPLUS A/i'          =>  'OnePlus Series',
        '/Mi 13/i'              =>  'Xiaomi Mi 13',
        '/Mi 12/i'              =>  'Xiaomi Mi 12',
        '/Mi 11/i'              =>  'Xiaomi Mi 11',
        '/Redmi Note 12/i'      =>  'Xiaomi Redmi Note 12',
        '/Redmi Note 11/i'      =>  'Xiaomi Redmi Note 11',
        
        // Other OS
        '/ubuntu/i'             =>  'Ubuntu',
        '/linux/i'              =>  'Linux',
        '/blackberry/i'         =>  'BlackBerry',
        '/webos/i'              =>  'Mobile WebOS'
    );

    foreach ($os_array as $regex => $value)
        if (preg_match($regex, $user_agent))
            return $value;

    return $os_platform;
}

function getBrowser() {
    global $user_agent;
    $browser = "Unknown Browser";
    $browser_array = array(
        '/msie 11/i'            => 'Internet Explorer 11',
        '/msie 10/i'            => 'Internet Explorer 10',
        '/msie 9/i'             => 'Internet Explorer 9',
        '/msie 8/i'             => 'Internet Explorer 8',
        '/msie 7/i'             => 'Internet Explorer 7',
        '/msie 6/i'             => 'Internet Explorer 6',
        '/msie/i'               => 'Internet Explorer',
        
        '/edge\/([0-9]+)/i'     => 'Edge $1',
        '/edg\/([0-9]+)/i'      => 'Edge $1',
        
        '/firefox\/([0-9]+)/i'  => 'Firefox $1',
        
        '/chrome\/([0-9]+)/i'   => 'Chrome $1',
        
        '/safari\/[0-9\.]+$/i'  => 'Safari',
        '/version\/([0-9]+).*safari/i' => 'Safari $1',
        
        '/opera mini/i'         => 'Opera Mini',
        '/opera\/([0-9]+)/i'    => 'Opera $1',
        '/opr\/([0-9]+)/i'      => 'Opera $1',
        
        '/ucbrowser\/([0-9\.]+)/i' => 'UC Browser $1',
        '/samsungbrowser\/([0-9\.]+)/i' => 'Samsung Browser $1',
        '/vivaldi\/([0-9\.]+)/i' => 'Vivaldi $1',
        '/brave\/([0-9\.]+)/i'  => 'Brave $1',
        
        '/android.*wv/i'        => 'Android WebView',
        '/fbav\/([0-9\.]+)/i'   => 'Facebook App $1',
        '/instagram/i'          => 'Instagram App',
        '/twitter/i'            => 'Twitter App',
        
        '/maxthon/i'            => 'Maxthon',
        '/konqueror/i'          => 'Konqueror',
        '/mobile/i'             => 'Mobile Browser'
    );

    foreach ($browser_array as $regex => $value) {
        if (preg_match($regex, $user_agent, $matches)) {
            if (count($matches) > 1) {
                return str_replace('$1', $matches[1], $value);
            }
            return $value;
        }
    }

    return $browser;
}

// You can call these functions like this:
// $os = getOS();
// $browser = getBrowser();
?>