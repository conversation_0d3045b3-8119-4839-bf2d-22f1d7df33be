<?php
    include '../core/dbcon.ini';
    include '../assets/common/header.php';
    include '../assets/common/design.php';
    
    $page_title = "Class Attendance";
    $current_page = "Student List";
    
 
    $subject_id = isset($_GET['subject_id']) ? (int)$_GET['subject_id'] : 1;
    

    $subjects = [
        1 => [
            'subject_name' => 'Quantitative Methods',
            'subject_code' => 'IT402',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 8:00-9:00 AM',
            'room' => 'Room 201'
        ],
        2 => [
            'subject_name' => 'System Integration and Architecture 2',
            'subject_code' => 'IT624',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 10:00-11:30 AM',
            'room' => 'Room 305'
        ],
        3 => [
            'subject_name' => 'System Administration and Maintenance',
            'subject_code' => 'IT271',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 2:00-3:30 PM',
            'room' => 'Room 402'
        ],
        4 => [
            'subject_name' => 'Software Engineering with Quality Testing and Evaluation',
            'subject_code' => 'IT421',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 1:00-2:30 PM',
            'room' => 'Room 501'
        ],
        5 => [
            'subject_name' => 'Social Issues and IT Professional Practice',
            'subject_code' => 'IT291',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 11:00-12:00 PM',
            'room' => 'Room 301'
        ],
        6 => [
            'subject_name' => 'Integrative Programming Technologies 2',
            'subject_code' => 'IT621',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 3:00-4:30 PM',
            'room' => 'Room 401'
        ],
        7 => [
            'subject_name' => 'IT Capstone Project 1',
            'subject_code' => 'IT431',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 1:00-2:00 PM',
            'room' => 'Room 502'
        ],
        8 => [
            'subject_name' => 'System Integration and Architecture 1',
            'subject_code' => 'IT321',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 8:00-9:30 AM',
            'room' => 'Room 203'
        ],
        9 => [
            'subject_name' => 'Civic Welfare Training Service',
            'subject_code' => 'NSTP2',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'SAT 7:00-10:00 AM',
            'room' => 'Room 101'
        ],
        10 => [
            'subject_name' => 'Application Development and Emerging Technologies',
            'subject_code' => 'IT106',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 3:00-4:00 PM',
            'room' => 'Room 404'
        ],
        11 => [
            'subject_name' => 'Multimedia and Graphics Designing 1',
            'subject_code' => 'IT221',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 2:00-3:30 PM',
            'room' => 'Room 302'
        ],
        12 => [
            'subject_name' => 'Advanced Database Management',
            'subject_code' => 'IT241',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'MWF 9:00-10:00 AM',
            'room' => 'Room 205'
        ],
        13 => [
            'subject_name' => 'Information Assurance and Security 1',
            'subject_code' => 'IT311',
            'section' => '3rd Yr. BSIT',
            'schedule' => 'TTH 4:00-5:30 PM',
            'room' => 'Room 503'
        ]
    ];
    
    $current_subject = $subjects[$subject_id] ?? $subjects[1];
    
    // Hardcoded students data for the selected subject
    $students = [
        [
            'id' => 1,
            'student_no' => '123456',
            'first_name' => 'Glenn Mark',
            'last_name' => 'Ruz',
            'middle_name' => 'S.',
            'course' => 'BSIT',
            'year' => '1'
        ],
        [
            'id' => 2,
            'student_no' => '728373',
            'first_name' => 'Joe Vert',
            'last_name' => 'Matienzo',
            'middle_name' => 'A.',
            'course' => 'BSIT',
            'year' => '1'
        ],
        [
            'id' => 3,
            'student_no' => '34455',
            'first_name' => 'John Ralph',
            'last_name' => 'Jordico',
            'middle_name' => 'B.',
            'course' => 'BSIT',
            'year' => '1'
        ],
        [
            'id' => 4,
            'student_no' => '45433',
            'first_name' => 'Nessa Mae',
            'last_name' => 'Mata',
            'middle_name' => 'C.',
            'course' => 'BSIT',
            'year' => '1'
        ],
        [
            'id' => 5,
            'student_no' => '344551',
            'first_name' => 'Jimwel',
            'last_name' => 'Manguiat',
            'middle_name' => 'D.',
            'course' => 'BSIT',
            'year' => '1'
        ]
    ];

    // Sample class schedules data (sorted by date descending)
    $class_schedules = [
        [
            'date' => '2024-12-20',
            'total_students' => 35,
            'present_students' => 32,
            'absent_students' => 3,
            'id' => 5,
            'attendance_data' => [
                'BSIT-2022-001' => 'present',
                'BSIT-2022-002' => 'late',
                'BSIT-2022-003' => 'present',
                'BSIT-2022-004' => 'absent',
                'BSIT-2022-005' => 'present'
            ]
        ],
        [
            'date' => '2024-12-18',
            'total_students' => 35,
            'present_students' => 30,
            'absent_students' => 5,
            'id' => 4,
            'attendance_data' => [
                'BSIT-2022-001' => 'present',
                'BSIT-2022-002' => 'present',
                'BSIT-2022-003' => 'late',
                'BSIT-2022-004' => 'absent',
                'BSIT-2022-005' => 'absent'
            ]
        ],
        [
            'date' => '2024-12-16',
            'total_students' => 35,
            'present_students' => 33,
            'absent_students' => 2,
            'id' => 3,
            'attendance_data' => [
                'BSIT-2022-001' => 'present',
                'BSIT-2022-002' => 'present',
                'BSIT-2022-003' => 'present',
                'BSIT-2022-004' => 'present',
                'BSIT-2022-005' => 'late'
            ]
        ],
        [
            'date' => '2024-12-13',
            'total_students' => 35,
            'present_students' => 28,
            'absent_students' => 7,
            'id' => 2,
            'attendance_data' => [
                'BSIT-2022-001' => 'late',
                'BSIT-2022-002' => 'absent',
                'BSIT-2022-003' => 'present',
                'BSIT-2022-004' => 'absent',
                'BSIT-2022-005' => 'present'
            ]
        ],
        [
            'date' => '2024-12-11',
            'total_students' => 35,
            'present_students' => 31,
            'absent_students' => 4,
            'id' => 1,
            'attendance_data' => [
                'BSIT-2022-001' => 'present',
                'BSIT-2022-002' => 'present',
                'BSIT-2022-003' => 'present',
                'BSIT-2022-004' => 'late',
                'BSIT-2022-005' => 'absent'
            ]
        ]
    ];

    $current_date = date('Y-m-d');
    $current_time = date('h:i A');
?>

<body>
<?php include '../assets/common/title.php'; ?>

<div class="main-container" id="main-container">
    <?php include '../assets/common/sidebar.php'; ?>
</div>

<main class="main-wrapper">
    <?php require '../assets/common/topbar.php'; ?>
    
    <section class="section">
        <div class="container-fluid">
            <!-- ========== title-wrapper start ========== -->
            <div class="title-wrapper pt-10">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="title">
                            <h2><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?></h2>
                        </div>
                    </div>
                    <!-- end col -->
                    <div class="col-md-6">
                        <div class="breadcrumb-wrapper">
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="index.php">My Classes</a>
                                    </li>
                                    <li class="breadcrumb-item active" aria-current="page">
                                        <?php echo $current_subject['subject_code']; ?>
                                    </li>
                                </ol>
                            </nav>
                        </div>
                    </div>
                    <!-- end col -->
                </div>
                <!-- end row -->

                <!-- Subject Info Card -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-style mb-30" style="background: linear-gradient(135deg, #004AAD 0%, #0066CC 100%); color: white; padding: 40px 30px; border-radius: 12px;">
                            <div class="subject-header">
                                <h1 style="font-size: 36px; font-weight: 700; margin-bottom: 8px; color: white; line-height: 1.2;">
                                    <?php echo $current_subject['subject_name']; ?>
                                </h1>
                                <h3 style="font-size: 24px; font-weight: 500; margin: 0; color: rgba(255,255,255,0.9); line-height: 1.3;">
                                    <?php echo $current_subject['subject_code']; ?> <?php echo $current_subject['section']; ?>
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Class Schedules Card -->
                <div class="row">
                    <div class="col-12">
                        <div class="card-style mb-30">
                            <div class="title d-flex flex-wrap justify-content-between align-items-center">
                                <div class="left">
                                    <h6 class="text-medium mb-2">My Class Schedules</h6>
                                </div>
                                <div class="right">
                                    <button class="main-btn primary-btn btn-hover btn-sm" onclick="openAddScheduleModal()" style="background-color: #004AAD; border-color: #004AAD;">
                                        Add Class Schedule
                                    </button>
                                </div>
                            </div>

                            <div class="table-wrapper table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th><h6>Date</h6></th>
                                            <th><h6>No. of Students</h6></th>
                                            <th><h6>No. of Present</h6></th>
                                            <th><h6>Absent</h6></th>
                                            <th class="text-center"><h6>Action</h6></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach($class_schedules as $schedule): ?>
                                        <tr>
                                            <td>
                                                <p style="font-size: 14px; font-weight: 500;"><?php echo date('M j, Y', strtotime($schedule['date'])); ?></p>
                                            </td>
                                            <td>
                                                <p style="font-size: 14px;"><?php echo $schedule['total_students']; ?></p>
                                            </td>
                                            <td>
                                                <p style="font-size: 14px; color: #4CAF50; font-weight: 500;"><?php echo $schedule['present_students']; ?></p>
                                            </td>
                                            <td>
                                                <p style="font-size: 14px; color: #FF4B4B; font-weight: 500;"><?php echo $schedule['absent_students']; ?></p>
                                            </td>
                                            <td class="text-center">
                                                <button class="main-btn primary-btn btn-hover btn-sm" onclick="editSchedule(<?php echo $schedule['id']; ?>)" style="background-color: #FFC300; border-color: #FFC300; color: #000; font-size: 12px; padding: 6px 12px;">
                                                    Update
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>
    <!-- Add Class Schedule Modal -->
    <div class="modal fade" id="addScheduleModal" tabindex="-1" aria-labelledby="addScheduleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #004AAD; color: white;">
                    <h5 class="modal-title" id="addScheduleModalLabel">Add Class Schedule</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="scheduleForm">
                        <div class="mb-3">
                            <label for="scheduleDate" class="form-label" style="font-weight: 600;">Date</label>
                            <input type="date" class="form-control" id="scheduleDate" required>
                        </div>

                        <h6 class="mb-3" style="color: #004AAD; font-weight: 600;">Mark Attendance</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr style="background-color: #f8f9fa;">
                                        <th style="font-size: 13px; font-weight: 600;">Student No.</th>
                                        <th style="font-size: 13px; font-weight: 600;">Name</th>
                                        <th class="text-center" style="font-size: 13px; font-weight: 600;">Present</th>
                                        <th class="text-center" style="font-size: 13px; font-weight: 600;">Late</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach($students as $student): ?>
                                    <tr>
                                        <td style="font-size: 12px;"><?php echo $student['student_no']; ?></td>
                                        <td style="font-size: 12px;"><?php echo $student['last_name'] . ', ' . $student['first_name'] . ' ' . $student['middle_name']; ?></td>
                                        <td class="text-center">
                                            <div class="form-check d-flex justify-content-center">
                                                <input class="form-check-input" type="checkbox" name="modal_present[]" value="<?php echo $student['student_no']; ?>" id="modal_present_<?php echo $student['student_no']; ?>">
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <div class="form-check d-flex justify-content-center">
                                                <input class="form-check-input" type="checkbox" name="modal_late[]" value="<?php echo $student['student_no']; ?>" id="modal_late_<?php echo $student['student_no']; ?>">
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveSchedule()" style="background-color: #004AAD; border-color: #004AAD;">Save Schedule</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Class Schedule Modal -->
    <div class="modal fade" id="editScheduleModal" tabindex="-1" aria-labelledby="editScheduleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #FFC300; color: #000;">
                    <h5 class="modal-title" id="editScheduleModalLabel">Edit Class Schedule</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editScheduleForm">
                        <div class="mb-3">
                            <label for="editScheduleDate" class="form-label" style="font-weight: 600;">Date</label>
                            <input type="date" class="form-control" id="editScheduleDate" readonly style="background-color: #f8f9fa;">
                        </div>

                        <h6 class="mb-3" style="color: #004AAD; font-weight: 600;">Update Attendance</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr style="background-color: #f8f9fa;">
                                        <th style="font-size: 13px; font-weight: 600;">Student No.</th>
                                        <th style="font-size: 13px; font-weight: 600;">Name</th>
                                        <th class="text-center" style="font-size: 13px; font-weight: 600;">Present</th>
                                        <th class="text-center" style="font-size: 13px; font-weight: 600;">Late</th>
                                        <th class="text-center" style="font-size: 13px; font-weight: 600;">Absent</th>
                                    </tr>
                                </thead>
                                <tbody id="editStudentsList">
                                    <!-- Students will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-warning" onclick="updateSchedule()" style="background-color: #FFC300; border-color: #FFC300; color: #000;">Update Schedule</button>
                </div>
            </div>
        </div>
    </div>

    <?php include '../assets/common/footer.php'; ?>
</main>
<?php include '../assets/common/scripts.php'; ?>

<script>
// Real-time clock
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
    });
    document.getElementById('current-time').textContent = timeString;
}

// Update time every second
setInterval(updateTime, 1000);

// Schedule data for JavaScript access
const scheduleData = <?php echo json_encode($class_schedules); ?>;
const studentsData = <?php echo json_encode($students); ?>;

// Modal and schedule management
document.addEventListener('DOMContentLoaded', function() {

    // Modal checkbox handling - prevent checking both present and late
    const modalCheckboxes = document.querySelectorAll('#addScheduleModal input[type="checkbox"]');
    modalCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                const studentNo = this.value;
                const isPresent = this.name === 'modal_present[]';
                const otherCheckbox = document.querySelector(
                    `#addScheduleModal input[name="${isPresent ? 'modal_late[]' : 'modal_present[]'}"][value="${studentNo}"]`
                );
                if (otherCheckbox) {
                    otherCheckbox.checked = false;
                }
            }
        });
    });
});

// Open Add Schedule Modal
function openAddScheduleModal() {
    // Set today's date as default
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('scheduleDate').value = today;

    // Clear all checkboxes
    const modalCheckboxes = document.querySelectorAll('#addScheduleModal input[type="checkbox"]');
    modalCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
    });

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('addScheduleModal'));
    modal.show();
}

// Save Schedule
function saveSchedule() {
    const date = document.getElementById('scheduleDate').value;
    const presentCheckboxes = document.querySelectorAll('#addScheduleModal input[name="modal_present[]"]:checked');
    const lateCheckboxes = document.querySelectorAll('#addScheduleModal input[name="modal_late[]"]:checked');

    if (!date) {
        alert('Please select a date');
        return;
    }

    const presentStudents = Array.from(presentCheckboxes).map(cb => cb.value);
    const lateStudents = Array.from(lateCheckboxes).map(cb => cb.value);
    const totalPresent = presentStudents.length + lateStudents.length;
    const totalStudents = <?php echo count($students); ?>;
    const absent = totalStudents - totalPresent;

    // For now, just show an alert with the data
    let message = `Class Schedule Added!\n\n`;
    message += `Date: ${date}\n`;
    message += `Total Students: ${totalStudents}\n`;
    message += `Present: ${totalPresent}\n`;
    message += `Absent: ${absent}\n\n`;

    if (presentStudents.length > 0) {
        message += `Present Students: ${presentStudents.join(', ')}\n`;
    }
    if (lateStudents.length > 0) {
        message += `Late Students: ${lateStudents.join(', ')}`;
    }

    alert(message);

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addScheduleModal'));
    modal.hide();

    // In a real implementation, you would send this data to the server
    // and refresh the page or update the table dynamically
}

// Edit Schedule
function editSchedule(scheduleId) {
    // Find the schedule data
    const schedule = scheduleData.find(s => s.id == scheduleId);
    if (!schedule) {
        alert('Schedule not found!');
        return;
    }

    // Set the date (readonly)
    document.getElementById('editScheduleDate').value = schedule.date;

    // Clear and populate the students list
    const tbody = document.getElementById('editStudentsList');
    tbody.innerHTML = '';

    studentsData.forEach(student => {
        const studentNo = student.student_no;
        const attendance = schedule.attendance_data[studentNo] || 'absent';

        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="font-size: 12px;">${student.student_no}</td>
            <td style="font-size: 12px;">${student.last_name}, ${student.first_name} ${student.middle_name}</td>
            <td class="text-center">
                <div class="form-check d-flex justify-content-center">
                    <input class="form-check-input edit-attendance" type="radio" name="edit_attendance_${studentNo}" value="present" id="edit_present_${studentNo}" ${attendance === 'present' ? 'checked' : ''}>
                </div>
            </td>
            <td class="text-center">
                <div class="form-check d-flex justify-content-center">
                    <input class="form-check-input edit-attendance" type="radio" name="edit_attendance_${studentNo}" value="late" id="edit_late_${studentNo}" ${attendance === 'late' ? 'checked' : ''}>
                </div>
            </td>
            <td class="text-center">
                <div class="form-check d-flex justify-content-center">
                    <input class="form-check-input edit-attendance" type="radio" name="edit_attendance_${studentNo}" value="absent" id="edit_absent_${studentNo}" ${attendance === 'absent' ? 'checked' : ''}>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });

    // Store the schedule ID for updating
    document.getElementById('editScheduleModal').setAttribute('data-schedule-id', scheduleId);

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('editScheduleModal'));
    modal.show();
}

// Update Schedule
function updateSchedule() {
    const scheduleId = document.getElementById('editScheduleModal').getAttribute('data-schedule-id');
    const date = document.getElementById('editScheduleDate').value;

    // Collect attendance data
    const attendanceData = {};
    let presentCount = 0;
    let lateCount = 0;
    let absentCount = 0;

    studentsData.forEach(student => {
        const studentNo = student.student_no;
        const selectedRadio = document.querySelector(`input[name="edit_attendance_${studentNo}"]:checked`);
        const status = selectedRadio ? selectedRadio.value : 'absent';

        attendanceData[studentNo] = status;

        if (status === 'present') presentCount++;
        else if (status === 'late') lateCount++;
        else absentCount++;
    });

    const totalPresent = presentCount + lateCount;

    // Show confirmation
    let message = `Schedule Updated!\n\n`;
    message += `Date: ${date}\n`;
    message += `Total Students: ${studentsData.length}\n`;
    message += `Present: ${presentCount}\n`;
    message += `Late: ${lateCount}\n`;
    message += `Absent: ${absentCount}\n`;

    alert(message);

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('editScheduleModal'));
    modal.hide();

    // In a real implementation, you would send this data to the server
    // and refresh the page or update the table dynamically
}
</script>

</body>
</html>
