$(document).ready(function() {
    // Initialize elements
    const chatBubble = $('.chat-bubble-toggle');
    const chatPanel = $('.chat-panel');
    const expandBtn = $('.chat-expand-btn');
    const dockChat = $('#dockChat');
    let userProfilePicture = '../assets/images/profile/uploads/default.jpg'; // Default profile picture

    // Add resize event listener
    let resizeTimeout;
    $(window).on('resize', function() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function() {
            if (chatPanel.hasClass('expanded')) {
                chatPanel.removeClass('expanded');
                expandBtn.attr('title', 'Expand chat');
            }
            // Ensure proper scrolling after resize
            if (chatPanel.hasClass('active')) {
                scrollToBottom();
            }
        }, 250); // Debounce resize events
    });

    // Fetch user's profile picture
    $.ajax({
        url: '../ai/get_user_profile.php',
        method: 'GET',
        success: function(response) {
            if (response.status === 'success') {
                userProfilePicture = response.profile_picture;
            }
        }
    });
    
    // Handle expand/collapse
    expandBtn.click(function() {
        chatPanel.toggleClass('expanded');
        // Update button title based on current state
        $(this).attr('title', chatPanel.hasClass('expanded') ? 'Collapse chat' : 'Expand chat');
        // Force redraw of icons
        $(this).find('.lni-frame-expand, .lni-frame-contract').css('display', '');
        setTimeout(() => {
            if (chatPanel.hasClass('expanded')) {
                $(this).find('.lni-frame-expand').hide();
                $(this).find('.lni-frame-contract').show();
            } else {
                $(this).find('.lni-frame-expand').show();
                $(this).find('.lni-frame-contract').hide();
            }
        }, 10);
        // Ensure proper scrolling after expand/collapse
        setTimeout(scrollToBottom, 300);
    });
    
    // Toggle chat panel function
    function toggleChatPanel() {
        chatPanel.toggleClass('active');
        chatBubble.toggleClass('active');
        if (chatPanel.hasClass('active')) {
            // Only focus input on desktop devices (not mobile)
            if (window.innerWidth > 768) {
                $('#chat-bubble-input').focus();
            }
            // Ensure proper scrolling when opening chat
            setTimeout(function() {
                scrollToBottom();
            }, 300);
        }
    }

    // Handle both chat bubble and dock chat clicks
    chatBubble.click(toggleChatPanel);
    dockChat.click(function(e) {
        e.preventDefault();
        toggleChatPanel();
    });
    
    // Close chat panel
    $('.chat-close-btn').click(function() {
        chatPanel.removeClass('active expanded');
        chatBubble.removeClass('active');
    });

    // Clear chat messages
    $('.chat-clear-btn').click(function() {
        $('.chat-panel-messages').empty();
        // Re-add initial greeting
        appendMessage("Hi! I'm your AI Assistant po. I can help you with student records, attendance, and answer any questions you have. Ano ang kailangan mo today?");
    });
    
    // Close chat panel when clicking outside
    $(document).on('click', function(event) {
        if (chatPanel.hasClass('active') && 
            !$(event.target).closest('.chat-panel').length && 
            !$(event.target).closest('.chat-bubble-toggle').length &&
            !$(event.target).closest('#dockChat').length) {
            chatPanel.removeClass('active expanded');
            chatBubble.removeClass('active');
        }
    });
    
    // Message handling functions
    let selectedFile = null;
    let selectedFilePreview = null;

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatMessage(message) {
        // Convert newlines to <br> tags
        return message.replace(/\n/g, '<br>');
    }

    function getCurrentTime() {
        return new Date().toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    }

    // Show typing indicator
    function showTypingIndicator() {
        // Remove any existing typing indicator first
        $('.typing-indicator').remove();
        
        const typingHtml = `
            <div class="typing-indicator">
                <div class="message-avatar">
                    <img src="../assets/images/avatars/technical.png" alt="AI Avatar">
                </div>
                <div class="message-bubble">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;
        
        $('.chat-panel-messages').append(typingHtml);
        
        // Scroll to bottom - improved for mobile
        scrollToBottom();
    }

    // Hide typing indicator
    function hideTypingIndicator() {
        $('.typing-indicator').remove();
    }
    
    // Improved scroll to bottom function that works better on mobile
    function scrollToBottom() {
        const messagesContainer = $('.chat-panel-messages');
        
        // First attempt with animation
        messagesContainer.animate({
            scrollTop: messagesContainer[0].scrollHeight
        }, 200);
        
        // Second attempt with timeout as a fallback for mobile
        setTimeout(function() {
            messagesContainer.scrollTop(messagesContainer[0].scrollHeight);
            
            // Additional attempt for stubborn mobile browsers
            setTimeout(function() {
                if (messagesContainer[0]) {
                    messagesContainer[0].scrollTop = messagesContainer[0].scrollHeight;
                    
                    // Force layout recalculation for mobile browsers
                    messagesContainer.css('padding-bottom', '80px');
                    messagesContainer[0].scrollTop = messagesContainer[0].scrollHeight;
                }
            }, 100);
        }, 250);
    }

    function appendMessage(message, isUser = false, fileData = null) {
        const messageClass = isUser ? 'message-sent' : 'message-received';
        const containerClass = isUser ? 'sent' : 'received';
        const time = getCurrentTime();
        
        let contentHtml = '';
        
        // Handle file preview if present
        if (fileData) {
            if (fileData.type.startsWith('image/')) {
                // For images, show preview
                contentHtml = `
                    <div class="file-preview mb-2">
                        <img src="${fileData.url}" alt="Uploaded file" style="max-width: 200px; max-height: 200px; border-radius: 8px;">
                    </div>
                    <p class="mb-0">${formatMessage(escapeHtml(message))}</p>
                `;
            } else {
                // For PDFs, show icon and filename
                contentHtml = `
                    <div class="file-preview mb-2">
                        <i class="lni lni-file-upload" style="font-size: 24px;"></i>
                        <span>${fileData.name}</span>
                    </div>
                    <p class="mb-0">${formatMessage(escapeHtml(message))}</p>
                `;
            }
        } else {
            // Regular message
            contentHtml = `<p class="mb-0">${formatMessage(escapeHtml(message))}</p>`;
        }
        
        // Use user's profile picture for user messages and technical.png for AI messages
        const avatarSrc = isUser ? userProfilePicture : '../assets/images/avatars/technical.png';
        
        const messageHtml = `
            <div class="message-container ${containerClass}">
                <div class="message-avatar">
                    <img src="${avatarSrc}" alt="${isUser ? 'User' : 'AI'} Avatar">
                </div>
                <div class="message-bubble ${messageClass}">
                    ${contentHtml}
                    <small class="chat-time d-block mt-1">${time}</small>
                </div>
            </div>
        `;
        
        $('.chat-panel-messages').append(messageHtml);
        
        // Keep only last 50 messages
        const $messages = $('.chat-panel-messages').children();
        if ($messages.length > 50) {
            $messages.slice(0, $messages.length - 50).remove();
        }
        
        // Scroll to bottom
        scrollToBottom();
    }
    
    // Send message on button click
    $('#chat-bubble-send').click(function() {
        const input = $('#chat-bubble-input');
        const message = input.val().trim();
        
        if (message || selectedFile) {
            // Disable input while processing
            input.prop('disabled', true);
            $(this).prop('disabled', true);
            $('#file-upload').prop('disabled', true);
            
            // Create file data for preview if file exists
            const fileData = selectedFile ? {
                name: selectedFile.name,
                type: selectedFile.type,
                url: selectedFilePreview
            } : null;

            // Show message with file preview if exists
            if (selectedFile && message) {
                appendMessage(`${message}\n(Analyzing: ${selectedFile.name})`, true, fileData);
            } else if (selectedFile) {
                appendMessage(`Please analyze this ${selectedFile.type.split('/')[1]}: ${selectedFile.name}`, true, fileData);
            } else {
                appendMessage(message, true);
            }
            input.val('');
            
            // Show typing indicator
            showTypingIndicator();
            
            if (selectedFile) {
                // If file exists, send for analysis with message
                const formData = new FormData();
                formData.append('file', selectedFile);
                if (message) {
                    formData.append('message', message);
                }

                $.ajax({
                    url: '../ai/process_doc.php',
                    method: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        // Hide typing indicator before showing response
                        hideTypingIndicator();
                        
                        if (response.type === 'success') {
                            // Format response for better readability
                            const analysis = response.message
                                .replace(/\*\*/g, '')
                                .split('\n\n')
                                .map(section => section.trim())
                                .filter(section => section)
                                .map(section => section.replace(/^\* /gm, '• '))
                                .join('\n\n');
                            
                            appendMessage(analysis, false);
                        } else {
                            appendMessage('Sorry po, may error sa pag-analyze ng file. Please try again.', false);
                        }
                    },
                    error: function() {
                        // Hide typing indicator before showing error
                        hideTypingIndicator();
                        appendMessage('Sorry po, may error sa pag-analyze ng file. Please try again.', false);
                    },
                    complete: function() {
                        // Clean up file data
                        if (selectedFilePreview) {
                            URL.revokeObjectURL(selectedFilePreview);
                        }
                        selectedFile = null;
                        selectedFilePreview = null;
                        updatePreviewArea();

                        // Re-enable inputs
                        input.prop('disabled', false);
                        // Only focus on desktop
                        if (window.innerWidth > 768) {
                            input.focus();
                        }
                        $('#chat-bubble-send').prop('disabled', false);
                        $('#file-upload').prop('disabled', false);
                    }
                });
            } else {
                // Regular chat message
                $.ajax({
                    url: '../ai/chat.php',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ message: message }),
                    success: function(response) {
                        // Hide typing indicator before showing response
                        hideTypingIndicator();
                        
                        if (response.type === 'success') {
                            appendMessage(response.message, false);
                        } else {
                            appendMessage('Sorry po, may error. Please try again.', false);
                        }
                    },
                    error: function() {
                        // Hide typing indicator before showing error
                        hideTypingIndicator();
                        appendMessage('Sorry po, may error. Please try again.', false);
                    },
                    complete: function() {
                        // Re-enable inputs
                        input.prop('disabled', false);
                        // Only focus on desktop
                        if (window.innerWidth > 768) {
                            input.focus();
                        }
                        $('#chat-bubble-send').prop('disabled', false);
                        $('#file-upload').prop('disabled', false);
                    }
                });
            }
        }
    });
    
    // Send message on Enter key
    $('#chat-bubble-input').keypress(function(e) {
        if (e.which === 13 && !$(this).prop('disabled')) {
            $('#chat-bubble-send').click();
        }
    });

    // Handle file uploads
    $('#file-upload').change(function(e) {
        const file = e.target.files[0];
        if (!file) return;

        // Clear the file input
        $(this).val('');

        // Clean up previous file if exists
        if (selectedFilePreview) {
            URL.revokeObjectURL(selectedFilePreview);
        }

        // Store new file
        selectedFile = file;
        if (file.type.startsWith('image/')) {
            selectedFilePreview = URL.createObjectURL(file);
        } else {
            selectedFilePreview = null;
        }

        // Update preview area
        updatePreviewArea();
        
        // Focus input for message only on desktop
        if (window.innerWidth > 768) {
            $('#chat-bubble-input').focus();
        }
    });

    // Handle remove file button
    $(document).on('click', '.remove-file', function(e) {
        e.preventDefault();
        e.stopPropagation(); // Stop event from bubbling up to parent elements
        
        if (selectedFilePreview) {
            URL.revokeObjectURL(selectedFilePreview);
        }
        selectedFile = null;
        selectedFilePreview = null;
        updatePreviewArea();
    });
    
    function updatePreviewArea() {
        let previewArea = $('.file-preview-area');
        if (previewArea.length === 0) {
            previewArea = $('<div class="file-preview-area mb-2" style="display:none"></div>');
            $('.chat-panel-input').prepend(previewArea);
        }

        if (selectedFile) {
            const html = `
                <div class="selected-file d-flex align-items-center">
                    ${selectedFile.type.startsWith('image/')
                        ? `<img src="${selectedFilePreview}" alt="Selected" class="preview-image">`
                        : `<i class="lni lni-file-upload file-icon"></i>`}
                    <span class="file-name" title="${selectedFile.name}">${selectedFile.name}</span>
                    <button class="btn remove-file" aria-label="Remove file">
                        <i class="lni lni-close"></i>
                    </button>
                </div>
            `;
            previewArea.html(html).show();
        } else {
            previewArea.hide().empty();
        }
    }

    // Add initial greeting
    appendMessage("Hi! I'm your AI Assistant po. I can help you with student records, attendance, and answer any questions you have. Upload documents or files for analysis, or ask me anything. Ano ang kailangan mo today?");

    // Handle mobile keyboard
    const chatInput = document.getElementById('chat-bubble-input');

    chatInput.addEventListener('focus', () => {
        document.body.classList.add('keyboard-open');
        setTimeout(() => {
            scrollToBottom();
        }, 300);
    });

    chatInput.addEventListener('blur', () => {
        document.body.classList.remove('keyboard-open');
        setTimeout(() => {
            scrollToBottom();
        }, 300);
    });

    // Detect keyboard height on Android/iOS
    window.addEventListener('resize', () => {
        if (document.body.classList.contains('keyboard-open')) {
            const visualViewport = window.visualViewport;
            if (visualViewport) {
                const keyboardHeight = window.innerHeight - visualViewport.height;
                document.documentElement.style.setProperty('--keyboard-height', `${keyboardHeight}px`);
                
                // Ensure messages are scrolled to bottom when keyboard appears
                setTimeout(() => {
                    scrollToBottom();
                }, 300);
            }
        }
    });
});
