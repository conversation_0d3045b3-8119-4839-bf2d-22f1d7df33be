<?php 
//session_start();
// Include the necessary files at the top of the file
include_once '../core/dbcon.ini';
include_once '../query/notifications.qry';

// Initialize the NOTIFICATIONS class
/*if (!isset($notifications)) {
    $notifications = new NOTIFICATIONS();
}

if (isset($_SESSION['alert'])) {
    $alertType = $_SESSION['alert']['type'];
    $alertMessage = $_SESSION['alert']['message'];
    //echo "
?>
    <div class='alert alert-{$alertType} alert-dismissible fade show m-3' role='alert' id='autoHideAlert'>
        <?php echo $alertMessage; ?>
        <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
    </div>
    <script>
        // Super simplified auto-hide alert
        setTimeout(() => document.getElementById('autoHideAlert')?.remove(), 3000);
    </script>
<?php
    unset($_SESSION['alert']); // Clear the alert after displaying
}*/
?>

	  
	  <!-- ========== header start ========== -->
<header class="header" style="background-color:#EFBF04;">
        <div class="container-fluid">
          <div class="row">
            <div class="col-md-5 col-md-5 col-6">
              <div class="header-left d-flex align-items-center">
                <div class="menu-toggle-btn mr-15">
                  <button id="menu-toggle" class="main-btn primary-btn btn-hover">
                    <i class="lni lni-chevron-left me-2"></i> 
                  </button>
                </div>
                <!--<div class="header-search d-none d-md-flex">
                  <form action="#">
                    <input type="text" placeholder="Search..." />--
					<img src="../assets/images/logo/dbc.png" alt="DBC Logo" style="max-height: 20px; width: auto;">
					<label class="" style="color:#1E2F97;font-style:bold;font-size:20pt;" >DON BOSCO COLLEGE, INC.</label>
                    <!--<button><i class="lni lni-search-alt"></i></button>
                  </form>
                </div>-->
              </div>
            </div>
            <div class="col-md-7 col-md-7 col-6">
              <div class="header-right">
                <!-- notification start -->
                <div class="notification-box ml-15 d-none d-md-flex">
                  <button class="dropdown-toggle" type="button" id="notification" data-bs-toggle="dropdown"
                    aria-expanded="false">
                    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11 20.1667C9.88317 20.1667 8.88718 19.63 8.23901 18.7917H13.761C13.113 19.63 12.1169 20.1667 11 20.1667Z"
                        fill="" />
                      <path
                        d="M10.1157 2.74999C10.1157 2.24374 10.5117 1.83333 11 1.83333C11.4883 1.83333 11.8842 2.24374 11.8842 2.74999V2.82604C14.3932 3.26245 16.3051 5.52474 16.3051 8.24999V14.287C16.3051 14.5301 16.3982 14.7633 16.564 14.9352L18.2029 16.6342C18.4814 16.9229 18.2842 17.4167 17.8903 17.4167H4.10961C3.71574 17.4167 3.5185 16.9229 3.797 16.6342L5.43589 14.9352C5.6017 14.7633 5.69485 14.5301 5.69485 14.287V8.24999C5.69485 5.52474 7.60672 3.26245 10.1157 2.82604V2.74999Z"
                        fill="" />
                    </svg>
                    <span></span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notification">
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="../../assets/images/lead/lead-6.png" alt="" />
                        </div>
                        <div class="content">
                          <h6>
                            John Doe
                            <span class="text-regular">
                              comment on a product.
                            </span>
                          </h6>
                          <p>
                            Lorem ipsum dolor sit amet, consect etur adipiscing
                            elit Vivamus tortor.
                          </p>
                          <span>10 mins ago</span>
                        </div>
                      </a>
                    </li>
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="../assets/images/lead/lead-6.jpg" alt="" />
                        </div>
                        <div class="content">
                          <h6>
                            Jonathon
                            <span class="text-regular">
                              like on a product.
                            </span>
                          </h6>
                          <p>
                            Lorem ipsum dolor sit amet, consect etur adipiscing
                            elit Vivamus tortor.
                          </p>
                          <span>10 mins ago</span>
                        </div>
                      </a>
                    </li>
                  </ul>
                </div>
                <!-- notification end -->
                <!-- message start -->
                <!--<div class="header-message-box ml-15 d-none d-md-flex">
                  <button class="dropdown-toggle" type="button" id="message" data-bs-toggle="dropdown"
                    aria-expanded="false">
                    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M7.74866 5.97421C7.91444 5.96367 8.08162 5.95833 8.25005 5.95833C12.5532 5.95833 16.0417 9.4468 16.0417 13.75C16.0417 13.9184 16.0364 14.0856 16.0259 14.2514C16.3246 14.138 16.6127 14.003 16.8883 13.8482L19.2306 14.629C19.7858 14.8141 20.3141 14.2858 20.129 13.7306L19.3482 11.3882C19.8694 10.4604 20.1667 9.38996 20.1667 8.25C20.1667 4.70617 17.2939 1.83333 13.75 1.83333C11.0077 1.83333 8.66702 3.55376 7.74866 5.97421Z"
                        fill="" />
                      <path
                        d="M14.6667 13.75C14.6667 17.2938 11.7939 20.1667 8.25004 20.1667C7.11011 20.1667 6.03962 19.8694 5.11182 19.3482L2.76946 20.129C2.21421 20.3141 1.68597 19.7858 1.87105 19.2306L2.65184 16.8882C2.13062 15.9604 1.83338 14.89 1.83338 13.75C1.83338 10.2062 4.70622 7.33333 8.25004 7.33333C11.7939 7.33333 14.6667 10.2062 14.6667 13.75ZM5.95838 13.75C5.95838 13.2437 5.54797 12.8333 5.04171 12.8333C4.53545 12.8333 4.12504 13.2437 4.12504 13.75C4.12504 14.2563 4.53545 14.6667 5.04171 14.6667C5.54797 14.6667 5.95838 14.2563 5.95838 13.75ZM9.16671 13.75C9.16671 13.2437 8.7563 12.8333 8.25004 12.8333C7.74379 12.8333 7.33338 13.2437 7.33338 13.75C7.33338 14.2563 7.74379 14.6667 8.25004 14.6667C8.7563 14.6667 9.16671 14.2563 9.16671 13.75ZM11.4584 14.6667C11.9647 14.6667 12.375 14.2563 12.375 13.75C12.375 13.2437 11.9647 12.8333 11.4584 12.8333C10.9521 12.8333 10.5417 13.2437 10.5417 13.75C10.5417 14.2563 10.9521 14.6667 11.4584 14.6667Z"
                        fill="" />
                    </svg>
                    <span></span>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="message">
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="assets/images/lead/lead-5.png" alt="" />
                        </div>
                        <div class="content">
                          <h6>Jacob Jones</h6>
                          <p>Hey!I can across your profile and ...</p>
                          <span>10 mins ago</span>
                        </div>
                      </a>
                    </li>
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="assets/images/lead/lead-3.png" alt="" />
                        </div>
                        <div class="content">
                          <h6>John Doe</h6>
                          <p>Would you mind please checking out</p>
                          <span>12 mins ago</span>
                        </div>
                      </a>
                    </li>
                    <li>
                      <a href="#0">
                        <div class="image">
                          <img src="assets/images/lead/lead-2.png" alt="" />
                        </div>
                        <div class="content">
                          <h6>Anee Lee</h6>
                          <p>Hey! are you available for freelance?</p>
                          <span>1h ago</span>
                        </div>
                      </a>
                    </li>
                  </ul>
                </div>-->
                <!-- message end -->
                <!-- profile start -->
                <div class="profile-box ml-15">
                  <button class="dropdown-toggle bg-transparent border-0" type="button" id="profile"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="profile-info">
                      <div class="info">
                        <div class="image">
                          <img src="../assets/images/lead/images.jpg" alt="" />
                        </div>
                        <div>
                          <h6 class="fw-500"><?php echo $_SESSION['name']; ?></h6>
                          <p><?php echo $_SESSION['role']; ?></p>
                        </div>
                      </div>
                    </div>
                  </button>
                  <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profile">
                    <li>
                      <div class="author-info flex items-center !p-1">
                        <div class="image">
                          <img src="../assets/images/lead/images.jpg" alt="image">
                        </div>
                        <div class="content">
                          <h4 class="text-sm">Adam Joe</h4>
                          <a class="text-black/40 dark:text-white/40 hover:text-black dark:hover:text-white text-xs" href="#"><EMAIL></a>
                        </div>
                      </div>
                    </li>
                    <li class="divider"></li>
                    <li>
                      <a href="#0">
                        <i class="lni lni-user"></i> View Profile
                      </a>
                    </li>
                    <li>
                      <a href="#0">
                        <i class="lni lni-alarm"></i> Notifications
                      </a>
                    </li>
                    <li>
                      <a href="#0"> <i class="lni lni-inbox"></i> Messages </a>
                    </li>
                    <li>
                      <a href="#0"> <i class="lni lni-cog"></i> Settings </a>
                    </li>
                    <li class="divider"></li>
                    <li>
                      <a href="#0"> <i class="lni lni-exit"></i> Sign Out </a>
                    </li>
                  </ul>
                </div>
                <!-- profile end -->
              </div>
            </div>
          </div>
        </div>
      </header>
<!-- ========== header end ========== -->

<!-- Mobile Floating Dock -->
<div class="floating-dock">
  <a href="#" class="dock-item" id="dockMenu">
    <i class="lni lni-menu"></i>
    <span>Menu</span>
  </a>
  <a href="notifications.php" class="dock-item position-relative">
    <i class="lni lni-alarm"></i>
    <?php //if ($unreadCount > 0): ?>
    <span class="position-absolute top-0 start-100 translate-middle notification-dot-mobile">
      <span class="visually-hidden">New alerts</span>
    </span>
    <?php //endif; ?>
    <span>Notifications</span>
  </a>
  <a href="#" class="dock-item" id="themeToggle">
    <i class="lni lni-sun"></i>
    <span>Theme</span>
  </a>
  <a href="#" class="dock-item" data-bs-toggle="modal" data-bs-target="#profileModal">
    <i class="lni lni-user"></i>
    <span>Profile</span>
  </a>
</div>

<!-- Profile Modal -->
<div class="modal fade profile-modal" id="profileModal" tabindex="-1" role="dialog" aria-labelledby="profileModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content shadow">
      <div class="modal-header border-bottom">
        <h5 class="modal-title fw-bold" id="profileModalLabel">My Profile</h5>
        <button type="button" class="btn-close bg-light" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body p-4">
        <div class="text-center mb-4">
          <div class="mx-auto mb-4" style="width: 150px; height: 150px;">
            <img src="../assets/images/profile/uploads/<?php echo isset($_SESSION['profile_picture']) ? $_SESSION['profile_picture'] : 'profile-image.png'; ?>" class="rounded-circle w-100 h-100" style="object-fit: cover;" id="currentProfilePic">
          </div>
          <span class="h4 text-dark dark:text-white" style="font-style: normal; font-weight: 600; display: block; margin-bottom: 15px;">
            <?php echo isset($_SESSION['name']) ? $_SESSION['name'] : 'Guest'; ?>
          </span>
          <button class="btn btn-secondary btn-sm rounded-pill" onclick="toggleEditMode()">
            <i class="lni lni-pencil me-1"></i> Edit Profile
          </button>
        </div>

        <div id="editProfileForm" style="display: none;">
          <!-- Profile Picture Form -->
          <form action="scripts/user_profile.php" method="post" id="profilePictureForm" enctype="multipart/form-data" class="mt-3 mb-4">
            <div class="profile-upload-container" id="uploadContainer">
              <i class="lni lni-image"></i>
              <p class="upload-text">Drag & drop your profile picture here<br>or click to select</p>
              <p class="upload-hint">Supported formats: JPG, PNG, GIF (Max 5MB)</p>
              <input type="file" class="form-control d-none" id="profileImage" name="profileImage" accept="image/*">
            </div>
            <div class="invalid-feedback" id="fileError"></div>
            <div class="progress upload-progress" id="uploadProgress">
              <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
            <div class="text-end">
              <button type="submit" name="updatePicture" class="btn btn-secondary btn-sm rounded-pill" id="uploadButton" disabled>
                <i class="lni lni-upload me-1"></i> Update Picture
              </button>
            </div>
          </form>

          <hr class="my-4">
          
          <!-- Password Form -->
          <form action="scripts/user_profile.php" method="post" id="passwordForm" class="mt-3">
            <div class="form-group mb-3">
              <label for="currentPassword">Current Password</label>
              <input type="password" class="form-control" id="currentPassword" name="currentPassword" placeholder="Enter current password">
            </div>
            <div class="form-group mb-3">
              <label for="newPassword">New Password</label>
              <input type="password" class="form-control" id="newPassword" name="newPassword" placeholder="Enter new password">
            </div>
            <div class="form-group mb-3">
              <label for="confirmPassword">Confirm New Password</label>
              <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" placeholder="Re-enter new password">
            </div>
            
            <div class="modal-footer border-top mt-4">
              <button type="button" class="btn btn-danger btn-sm rounded-pill" onclick="toggleEditMode()">
                <i class="lni lni-close me-1"></i> Cancel
              </button>
              <button type="submit" name="updatePassword" class="btn btn-secondary btn-sm rounded-pill">
                <i class="lni lni-lock me-1"></i> Update Password
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
function toggleEditMode() {
  const editForm = document.getElementById('editProfileForm');
  const isHidden = editForm.style.display === 'none';
  editForm.style.display = isHidden ? 'block' : 'none';
}

function validateFile(file) {
  const maxSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
  const fileError = document.getElementById('fileError');
  const uploadButton = document.getElementById('uploadButton');
  
  if (!file) {
    fileError.textContent = 'Please select a file.';
    fileError.style.display = 'block';
    uploadButton.disabled = true;
    return false;
  }
  
  if (!allowedTypes.includes(file.type)) {
    fileError.textContent = 'Please select a valid image file (JPG, PNG, or GIF).';
    fileError.style.display = 'block';
    uploadButton.disabled = true;
    return false;
  }
  
  if (file.size > maxSize) {
    fileError.textContent = 'File size must be less than 5MB.';
    fileError.style.display = 'block';
    uploadButton.disabled = true;
    return false;
  }
  
  fileError.style.display = 'none';
  uploadButton.disabled = false;
  return true;
}

function resizeImage(img, maxSize) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  let width = img.width;
  let height = img.height;
  
  if (width > height) {
    if (width > maxSize) {
      height = height * (maxSize / width);
      width = maxSize;
    }
  } else {
    if (height > maxSize) {
      width = width * (maxSize / height);
      height = maxSize;
    }
  }
  
  canvas.width = width;
  canvas.height = height;
  ctx.drawImage(img, 0, 0, width, height);
  
  return canvas.toDataURL('image/jpeg', 0.9);
}

// Handle drag and drop
const uploadContainer = document.getElementById('uploadContainer');
const fileInput = document.getElementById('profileImage');

['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
  uploadContainer.addEventListener(eventName, (e) => {
    e.preventDefault();
    e.stopPropagation();
  });
});

['dragenter', 'dragover'].forEach(eventName => {
  uploadContainer.addEventListener(eventName, () => {
    uploadContainer.classList.add('dragover');
  });
});

['dragleave', 'drop'].forEach(eventName => {
  uploadContainer.addEventListener(eventName, () => {
    uploadContainer.classList.remove('dragover');
  });
});

uploadContainer.addEventListener('drop', (e) => {
  const files = e.dataTransfer.files;
  if (files.length) {
    fileInput.files = files;
    handleFileSelect(files[0]);
  }
});

uploadContainer.addEventListener('click', () => fileInput.click());

// Handle file selection
fileInput.addEventListener('change', (e) => {
  const file = e.target.files[0];
  if (file) {
    handleFileSelect(file);
  }
});

function handleFileSelect(file) {
  if (!validateFile(file)) return;
  
  const reader = new FileReader();
  reader.onload = function(e) {
    const img = new Image();
    img.onload = function() {
      const resizedImage = resizeImage(img, 500);
      document.getElementById('currentProfilePic').src = resizedImage;
      
      // Show upload progress bar simulation
      const progressBar = document.querySelector('.upload-progress');
      const progressBarInner = progressBar.querySelector('.progress-bar');
      progressBar.style.display = 'block';
      let progress = 0;
      const interval = setInterval(() => {
        progress += 5;
        progressBarInner.style.width = `${progress}%`;
        if (progress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            progressBar.style.display = 'none';
            progressBarInner.style.width = '0%';
          }, 500);
        }
      }, 50);
    };
    img.src = e.target.result;
  };
  reader.readAsDataURL(file);
}

// Form submission
document.getElementById('profilePictureForm').addEventListener('submit', (e) => {
  const fileInput = document.getElementById('profileImage');
  if (!fileInput.files.length) {
    e.preventDefault();
    document.getElementById('fileError').textContent = 'Please select a file to upload.';
    document.getElementById('fileError').style.display = 'block';
  }
});
</script>
