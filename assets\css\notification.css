/* Notification Styles */

/* Notification dot for topbar */
.notification-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 6px;
  height: 6px;
  background-color: #f44336;
  border-radius: 50%;
}

/* Mobile notification dot */
.notification-dot-mobile {
  width: 6px;
  height: 6px;
  background-color: #f44336;
  border-radius: 50%;
}

/* Notification dropdown styling */
.notification-dropdown {
  min-width: 300px;
  padding: 0;
}

.notification-dropdown .dropdown-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f1f1f1;
  transition: background-color 0.2s ease;
}

.notification-dropdown .dropdown-item:last-child {
  border-bottom: none;
}

.notification-dropdown .dropdown-item:hover {
  background-color: #f8f9fa;
}

/* Notification icon container */
.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  background-color: #4361ee;
  color: #ffffff;
}

.notification-icon i {
  font-size: 14px;
}

/* Notification content */
.notification-content {
  flex: 1;
  overflow: hidden;
}

.notification-content .notification-title {
  font-size: 13px;
  margin-bottom: 2px;
  color: #333;
  font-weight: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.notification-content .notification-time {
  font-size: 11px;
  color: #718096;
}

/* Unread notification styling */
.notification-unread {
  background-color: rgba(67, 97, 238, 0.03);
}

/* View all link */
.view-all-link {
  text-align: center;
  padding: 8px;
  font-size: 13px;
  color: #4361ee;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.view-all-link a {
  color: #4361ee;
  text-decoration: none;
}

.view-all-link:hover {
  background-color: #e9ecef;
}
