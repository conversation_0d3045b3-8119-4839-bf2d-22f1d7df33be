/* Profile Header in Dropdown */
.profile-dropdown .dropdown-header {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

/* Header Right Section Fixes */
.header .header-right {
  display: flex;
  align-items: center;
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  width: auto;
  z-index: 10;
}

.header .header-right .profile-box {
  display: flex;
  align-items: center;
  position: relative;
  width: auto;
}

.header .header-right .profile-box .profile-info {
  display: flex;
  align-items: center;
}

.header .header-right .profile-box .profile-info .info {
  display: flex;
  align-items: center;
}

/* Maintain consistent profile positioning across all screen sizes */
@media (max-width: 1400px), 
       (max-width: 1200px), 
       (max-width: 992px), 
       (max-width: 768px) {
  .header .header-right {
    right: 40px;
  }

  .header .header-right .profile-box {
    width: auto;
  }

  .header .header-right .notification-box,
  .header .header-right .header-message-box {
    margin-right: 15px;
  }
}

/* Adjust spacing for smaller screens */
@media (max-width: 576px) {
  .header .header-right {
    right: 20px;
  }

  .header .header-right .notification-box,
  .header .header-right .header-message-box {
    margin-right: 10px;
  }
}

/* Custom CSS for the profile upload */
.profile-upload-container {
  border: 2px dashed #ccc;
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 15px;
}

.dark-mode .profile-upload-container {
  border-color: #4a5568;
  background: #2d3748;
}

.profile-upload-container:hover,
.profile-upload-container.dragover {
  border-color: #0d6efd;
  background: #e9ecef;
}

.dark-mode .profile-upload-container:hover,
.dark-mode .profile-upload-container.dragover {
  border-color: #63b3ed;
  background: #2d3748;
}

.profile-upload-container i {
  font-size: 2rem;
  color: #6c757d;
  margin-bottom: 10px;
}

.dark-mode .profile-upload-container i {
  color: #a0aec0;
}

.profile-upload-container .upload-text {
  color: #495057;
  margin-bottom: 10px;
}

.dark-mode .profile-upload-container .upload-text {
  color: #e2e8f0;
}

.profile-upload-container .upload-hint {
  font-size: 0.875rem;
  color: #6c757d;
}

.dark-mode .profile-upload-container .upload-hint {
  color: #a0aec0;
}

.profile-preview-container {
  position: relative;
  width: 150px;
  height: 150px;
  margin: 0 auto 20px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.profile-preview-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-preview-container .change-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px;
  font-size: 0.875rem;
  text-align: center;
  cursor: pointer;
  transition: opacity 0.3s;
  opacity: 0;
}

.profile-preview-container:hover .change-overlay {
  opacity: 1;
}

.upload-progress {
  height: 4px;
  margin-top: 10px;
  display: none;
}

.upload-progress .progress-bar {
  transition: width 0.3s ease;
}

.invalid-feedback {
  display: none;
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 5px;
}

.dark-mode .invalid-feedback {
  color: #f56565;
}

/* Progress bar dark mode */
.dark-mode .progress {
  background-color: #2d3748;
}

.dark-mode .progress-bar {
  background-color: #4299e1;
}

/* Form elements dark mode */
.dark-mode .form-group label {
  color: #e2e8f0;
}

.dark-mode .form-control {
  background-color: #2d3748;
  border-color: #4a5568;
  color: #e2e8f0;
}

.dark-mode .form-control:focus {
  background-color: #2d3748;
  border-color: #63b3ed;
  color: #e2e8f0;
}

/* Header Title and Layout Adjustments */
.college-title {
    transition: all 0.3s ease;
  }
  
  .college-title img {
    height: 45px;
    margin-right: 15px;
    transition: all 0.3s ease;
  }
  
  .college-title h1 {
    font-size: 2rem;
    font-weight: bold;
    letter-spacing: 1px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    text-align: center;
    margin: 0;
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.3s ease;
  }
  
  /* Sidebar Menu Chevron Styling */
  .sidebar-nav .nav-item-has-children .chevron {
    margin-left: auto;
    transition: transform 0.3s ease;
  }
  
  .sidebar-nav .nav-item-has-children.active .chevron {
    transform: rotate(90deg);
  }
  
  /* Sidebar Menu Chevron Styling */
  .sidebar-nav .nav-item-has-children > a .mdi-chevron-down {
      margin-left: auto;
      transition: transform 0.3s ease;
      font-size: 15px;
      opacity: 0.8;
      position: absolute;
      right: 15px;
      top: 50%;
      transform: translateY(-50%);
  }
  
  .sidebar-nav .nav-item-has-children > a[aria-expanded="true"] .mdi-chevron-down {
      transform: translateY(-50%) rotate(180deg);
  }
  
  .sidebar-nav .nav-item-has-children > a:hover .mdi-chevron-down {
      opacity: 1;
  }
  
  /* Profile Styling */
  .profile-box .dropdown-menu .author-info .content .profile-dropdown-email {
    word-break: break-word;
    max-width: 200px;
    line-height: 1.2;
    margin-top: 2px;
    display: block;
    font-size: 12px;
    color: #718096;
  }
  
  @media (max-width: 400px) {
    .profile-box .dropdown-menu .author-info .content .profile-dropdown-email {
      max-width: 160px;
      font-size: 11px;
    }
  }
  
  @media (max-width: 1400px) {
    .college-title h1 {
      font-size: 1.75rem;
    }
    .college-title img {
      height: 40px;
    }
  }
  
  @media (max-width: 1200px) {
    .college-title h1 {
      font-size: 1.5rem;
    }
    .college-title img {
      height: 35px;
    }
  }
  
  @media (max-width: 992px) {
    .college-title h1 {
      font-size: 1.25rem;
    }
    .college-title img {
      height: 30px;
    }
  }
  
@media (max-width: 768px) {
  .header {
    min-height: 60px; /* lapad to ng yellow sa header */
  }
  .header .menu-toggle-btn {
    display: none;
  }
  .college-title {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
  }
  .college-title h1 {
    font-size: 1rem;
    max-width: 200px;
  }
  .college-title img {
    height: 25px;
    margin-right: 10px;
  }
}
  

/* Profile Responsive Adjustments - Header */
@media (max-width: 1200px) {
  .header .header-right .profile-box .profile-info .info h6 {
    font-size: 14px;
}

/* Mobile Notification Adjustments */
@media (max-width: 768px) {
  .single-notification .action {
    display: flex !important;
    visibility: visible !important;
    gap: 8px;
    margin-left: 10px;
  }
  
  .single-notification .action .delete-btn,
  .single-notification .action .more-btn {
    padding: 4px 8px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    color: #718096;
    transition: all 0.2s ease;
  }

  .single-notification .action .delete-btn:hover,
  .single-notification .action .more-btn:hover {
    color: #dc3545;
    transform: scale(1.1);
  }
}

/* Mobile Floating Dock - hidden on desktop */
.floating-dock,
.floating-dock * {
  display: none !important;
  visibility: hidden !important;
}

/* Footer Mobile Adjustments */
@media (max-width: 768px) {
  .footer {
    display: none !important;
  }
  
  .sidebar-nav .sidebar-footer {
    display: block;
    position: fixed;
    bottom: 80px;
    left: 0;
    width: 100%;
    padding: 15px;
    background: var(--sidebar-bg, #fff);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    font-size: 12px;
    text-align: center;
  }
  
  .sidebar-nav .sidebar-footer .copyright,
  .sidebar-nav .sidebar-footer .terms {
    margin: 5px 0;
  }
  
  .sidebar-nav .sidebar-footer a {
    color: inherit;
    text-decoration: none;
  }

/* Theme Toggle Styles */
.dark-mode {
  --sidebar-bg: #1e1e2d;
  --body-bg: #151521;
  --text-color: #e0e0e0;
  --dock-bg: rgba(30, 30, 45, 0.95);
  --dock-border: rgba(255, 255, 255, 0.1);
  --dock-item-color: #a0a0a0;
  --dock-active-bg: rgba(255, 255, 255, 0.1);
  --dock-active-color: #ffffff;
}

.floating-dock.show .dock-item#themeToggle i {
  transition: transform 0.3s ease;
}

.dark-mode .floating-dock.show {
  background: var(--dock-bg)	;
  border-color: var(--dock-border);
}

.dark-mode .floating-dock.show .dock-item {
  color: white;
}

.dark-mode .floating-dock.show .dock-item.active {
  background: var(--dock-active-bg);
  color: var(--dock-active-color);
}

/* Show only on mobile */
@media (max-width: 768px) {
  .floating-dock.show {
    display: flex !important;
    visibility: visible !important;
    position: fixed;
    bottom: 12px;
    left: 12px;
    right: 12px;
    max-width: 280px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    justify-content: space-around;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 6px 10px;
    z-index: 1000;
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: dockAppear 0.3s ease-out;
    transform-origin: bottom center;
  }

  @keyframes dockAppear {
    0% {
      transform: translateY(20px) scale(0.95);
      opacity: 0;
    }
    100% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }

  .floating-dock.show .dock-item {
    display: flex !important;
    visibility: visible !important;
    flex-direction: column;
    align-items: center;
    color: #718096;
    text-decoration: none;
    font-size: 10px;
    padding: 4px 6px;
    border-radius: 10px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  .floating-dock.show .dock-item i {
    display: inline-block !important;
    visibility: visible !important;
    font-size: 16px;
    margin-bottom: 1px;
    transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .floating-dock.show .dock-item.active {
    color: #007bff;
    background: rgba(0, 123, 255, 0.1);
  }

  .floating-dock.show .dock-item:active {
    transform: scale(0.9);
  }

  .floating-dock.show .dock-item:hover i {
    transform: translateY(-2px);
  }
  .floating-dock.show .dock-item span {
    display: inline-block !important;
    visibility: visible !important;
  }

  .floating-dock.show .dock-item.active {
    color: #007bff;
  }

  /* mobile view profile issue yung hindi makita */
  .header .header-right {
    display: flex; /* Changed from 'none' to 'flex' to make it visible */
    justify-content: flex-end; /* Maintain right alignment */
    align-items: center; /* Vertically align items */
    position: absolute; /* Keep absolute positioning for top/right control */
    right: 20px; /* Adjust right position for mobile */
    top: 50%;
    transform: translateY(-50%);
    width: auto;
    z-index: 10;
  }

  /* Add padding to main content to prevent overlap with dock */
  .main-wrapper {
    padding-bottom: 65px;
  }
}

/* College Title Length Handling */
.college-title h1.long-name {
  font-size: 1.6rem;
  letter-spacing: -0.5px;
}

@media (max-width: 1400px) {
  .college-title h1.long-name {
    font-size: 1.4rem;
  }
}
  .header .header-right .profile-box .profile-info .info p {
    font-size: 12px;
  }
}

@media (max-width: 992px) {
  .header .header-right .profile-box .profile-info .info h6 {
    font-size: 13px;
  }
  .header .header-right .profile-box .profile-info .info p {
    font-size: 11px;
  }
  .header .header-right .profile-box .profile-info .info {
    margin-right: 8px;
  }
}

@media (max-width: 768px) {
  .header .header-right .profile-box .profile-info .info h6,

  .header .header-right .profile-box .profile-info .info p {
    display: none; /* Hide name and role on mobile */
  }
  .header .header-right .profile-box .profile-info .info {
    margin-right: 0; /* Remove margin if text is hidden */
  }
  .header .header-right .profile-box .profile-info .info .image {
    width: 35px;
    height: 35px;
    margin-right: 8px;
  }
}

/* Enhanced Profile Dropdown Styling */
.profile-dropdown {
  position: absolute;
  right: 0;
  top: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  z-index: 100;
  width: 220px;
  padding: 5px 0;
}

/* Profile Header in Dropdown */
.profile-dropdown .dropdown-header {
  padding: 10px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

.profile-dropdown .dropdown-header img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 10px;
}

.profile-dropdown .dropdown-header .user-info {
  flex: 1;
  overflow: hidden;
}

.profile-dropdown .dropdown-header .user-info h4 {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
  line-height: 1.2;
}

.profile-dropdown .dropdown-header .user-info h4[data-length="long"] {
  font-size: 12px;
  letter-spacing: -0.3px;
  max-width: 135px;
}

.profile-dropdown .dropdown-header .user-info h4[data-length="very-long"] {
  font-size: 11px;
  letter-spacing: -0.5px;
  max-width: 130px;
}

.profile-dropdown .dropdown-header .user-info p {
  font-size: 12px;
  color: #858585;
  margin: 0;
}

/* Menu Items */
.profile-dropdown .dropdown-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.profile-dropdown .dropdown-menu li {
  padding: 0;
  margin: 0;
}

.profile-dropdown .dropdown-menu a {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  color: #333;
  text-decoration: none;
  font-size: 13px;
  transition: background-color 0.2s;
}

.profile-dropdown .dropdown-menu a:hover {
  background-color: #f5f5f5;
}

.profile-dropdown .dropdown-menu a svg,
.profile-dropdown .dropdown-menu a i {
  margin-right: 10px;
  width: 16px;
  height: 16px;
  color: #666;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
  .profile-dropdown {
    width: 200px;
  }
  
  .profile-dropdown .dropdown-header .user-info h4 {
    font-size: 13px;
    max-width: 130px;
  }
  
  .profile-dropdown .dropdown-header .user-info p {
    font-size: 11px;
  }
  
  .profile-dropdown .dropdown-menu a {
    padding: 7px 12px;
    font-size: 12px;
  }
  
  .profile-dropdown .dropdown-menu a svg,
  .profile-dropdown .dropdown-menu a i {
    width: 14px;
    height: 14px;
    margin-right: 8px;
  }
}

@media (max-width: 400px) {
  .profile-dropdown {
    width: 180px;
    right: -5px;
  }
  
  .profile-dropdown .dropdown-header {
    padding: 8px;
  }
  
  .profile-dropdown .dropdown-header img {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }
  
  .profile-dropdown .dropdown-header .user-info h4 {
    font-size: 12px;
    max-width: 110px;
    line-height: 1.2;
  }
  
  .profile-dropdown .dropdown-header .user-info p {
    font-size: 10px;
  }
  
  .profile-dropdown .dropdown-menu a {
    padding: 6px 10px;
    font-size: 11px;
  }
  
  /* Very long names */
  .profile-dropdown .dropdown-header .user-info h4.long-name {
    font-size: 11px;
    max-width: 100px;
    line-height: 1.1;
  }
}

/* Specific styling for extremely long names */
.profile-dropdown .dropdown-header .user-info h4[data-length="long"] {
  font-size: 12px;
  letter-spacing: -0.3px;
}

.profile-dropdown .dropdown-header .user-info h4[data-length="very-long"] {
  font-size: 11px;
  letter-spacing: -0.5px;
}

@media (max-width: 400px) {
  .profile-dropdown .dropdown-header .user-info h4[data-length="long"] {
    font-size: 11px;
    max-width: 90px;
  }
  
  .profile-dropdown .dropdown-header .user-info h4[data-length="very-long"] {
    font-size: 10px;
    max-width: 85px;
  }
}

/* Profile Modal Footer Responsive Fix */
@media (max-width: 576px) {
  .profile-modal .modal-footer {
    flex-direction: row !important;
    justify-content: space-between !important;
    gap: 10px;
    width: 100%;
    padding: 15px;
    display: flex !important;
  }
  
  .profile-modal .modal-footer button {
    margin: 0 !important;
    flex: 1;
    font-size: 13px;
    min-width: unset !important;
    padding: 8px 15px;
    white-space: nowrap;
  }
  
  .profile-modal .modal-footer button i {
    margin-right: 5px;
    font-size: 14px;
  }
}

@media (max-width: 400px) {
  .profile-modal .modal-footer {
    padding: 12px;
    gap: 8px;
  }
  
  .profile-modal .modal-footer button {
    font-size: 12px;
    padding: 6px 12px;
  }
}

/* Profile Info in Author Section */
.author-info .content h4 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}

.author-info .content h4[data-length="long"] {
  font-size: 12px;
  letter-spacing: -0.3px;
  max-width: 135px;
}

.author-info .content h4[data-length="very-long"] {
  font-size: 11px;
  letter-spacing: -0.5px;
  max-width: 130px;
}

@media (max-width: 400px) {
  .author-info .content h4[data-length="long"],
  .author-info .content h4[data-length="very-long"] {
    max-width: 90px;
  }
}
}

/* Dark mode styles for header profile text */
.dark-mode .dark-mode-text-override {
  color: #1E2F97 !important; /* Use a light color for visibility in dark mode */
}

/* Increase icon size in dashboard cards */
.card-icon i.lni {
  font-size: 3em; /* Adjust the size as needed */
}

/* --- Pagination & Entries Control --- */

/* Light Mode (Default) */
.pagination {
    justify-content: flex-end;
}
.page-item .page-link {
    color: #6c757d;
    border: 1px solid #dee2e6;
    margin: 0 2px;
    border-radius: 0.25rem;
    background-color: #fff; /* Explicitly set light background */
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}
.page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #0d6efd;
    border-color: #0d6efd;
}
.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
    border-color: #dee2e6;
}
.page-link:hover {
    z-index: 2;
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6;
}
.pagination-info {
    padding-top: 0.5rem;
    text-align: left;
    font-size: 0.9em;
    color: #6c757d;
}
.entries-control {
     padding-top: 0.5rem;
     padding-bottom: 1rem;
     display: flex;
     align-items: center;
}
.entries-control label {
    margin-right: 0.5rem;
    color: #6c757d;
}
.entries-control select.form-select { /* Be more specific */
    display: inline-block;
    width: auto;
    padding: 0.375rem 2rem 0.375rem 0.75rem; /* Adjusted padding for arrow */
    font-size: 0.9em;
    border-radius: 0.25rem;
    appearance: none; /* Hide default arrow */
    -webkit-appearance: none;
    -moz-appearance: none;
    position: relative; /* Needed for pseudo-element */
    /* Inherits default form-select styles like background-color, border */
}

/* Custom CSS Arrow for Light & Dark Mode */
.entries-control select.form-select::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0.75rem; /* Adjust position */
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 6px solid #6c757d; /* Neutral gray arrow color */
    pointer-events: none;
}


/* Dark Mode */
.dark-mode .page-item .page-link {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #a0aec0;
}
.dark-mode .page-item.active .page-link {
    background-color: #4299e1;
    border-color: #4299e1;
    color: #1a202c; /* Darker text on active blue */
}
.dark-mode .page-item.disabled .page-link {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #718096;
}
.dark-mode .page-link:hover {
    background-color: #4a5568;
    border-color: #63b3ed;
    color: #e2e8f0;
}
.dark-mode .pagination-info {
    color: #a0aec0;
}
.dark-mode .entries-control label {
    color: #a0aec0;
}
.dark-mode .entries-control select.form-select {
    /* appearance, position, padding-right moved to base rule */
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
    /* Custom arrow is now handled by the base ::after rule */
}

/* Dark mode specific ::after rule removed */

.dark-mode .entries-control select.form-select:focus {
    border-color: #63b3ed;
    box-shadow: 0 0 0 0.25rem rgba(99, 179, 237, 0.25);
}

/* --- End Pagination & Entries Control --- */

/* Dark mode styles for menu assignments pages */
.dark-mode .staff-avatar {
  background-color: #374151;
  color: #63b3ed;
}

.dark-mode .card .card-header {
  background-color: #1e2533 !important;
  border-bottom: 1px solid #2d3748;
}

.dark-mode .card .card-title {
  color: #63b3ed;
}

.dark-mode .card-header.bg-white {
  background-color: #1e2533 !important;
}

.dark-mode .table-light {
  background-color: #2d3748;
  color: #e2e8f0;
}

.dark-mode .table {
  color: #e2e8f0;
}

.dark-mode .table-hover tbody tr:hover {
  background-color: rgba(99, 179, 237, 0.1);
}

.dark-mode .text-muted {
  color: #a0aec0 !important;
}

.dark-mode .search-container .search-clear {
  color: #a0aec0;
}

.dark-mode .search-container .search-clear:hover {
  color: #63b3ed;
}

/* Dark mode for the accordion in edit_menu_assignments */
.dark-mode .menu-accordion .accordion-item {
  background-color: #1e2533;
  border-color: #2d3748;
}

.dark-mode .menu-accordion .accordion-button {
  background-color: #1e2533;
  color: #e2e8f0;
}

.dark-mode .menu-accordion .accordion-button:not(.collapsed) {
  background-color: #2d3748;
  color: #63b3ed;
}

.dark-mode .menu-accordion .accordion-button::after {
  filter: invert(1);
}

.dark-mode .menu-accordion .accordion-body {
  background-color: #1e2533;
  color: #e2e8f0;
}

.dark-mode .submenu-list {
  background-color: #2d3748 !important;
}

.dark-mode .submenu-item {
  border-bottom-color: #4a5568;
}

.dark-mode .form-check-label {
  color: #e2e8f0;
}

.dark-mode .form-check-input {
  background-color: #2d3748;
  border-color: #4a5568;
}

.dark-mode .form-check-input:checked {
  background-color: #3182ce;
  border-color: #3182ce;
}

.dark-mode .form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%233182ce'/%3e%3c/svg%3e");
}

.dark-mode .form-switch .form-check-input:checked {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.dark-mode .btn-outline-secondary {
  color: #a0aec0;
  border-color: #4a5568;
}

.dark-mode .btn-outline-secondary:hover {
  background-color: #4a5568;
  color: #e2e8f0;
  border-color: #4a5568;
}
