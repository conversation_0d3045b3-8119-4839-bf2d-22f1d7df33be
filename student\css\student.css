:root {
    /* Blue and Yellow Color Scheme */
    --primary-color: #004AAD; /* Deep Blue - Primary */
    --primary-hover: #0056d3;
    --accent-color: #FFD700; /* Bright Yellow - Secondary */
    --accent-hover: #FFC300;
    --success-color: #004AAD; /* Use blue instead of green */
    --error-color: #FFD700; /* Use yellow instead of red */
    --text-primary: #1F2937;
    --text-secondary: #6B7280;
    --bg-primary: #F8F9FA; /* Light background */
    --card-bg: #FFFFFF; /* White background */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow-soft: 0 4px 20px rgba(0, 74, 173, 0.1);
    --shadow-medium: 0 8px 30px rgba(0, 74, 173, 0.15);
}

body {
    font-family: 'Plus Jakarta Sans', sans-serif; /* Changed font to Plus Jakarta Sans */
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, var(--bg-primary) 0%, #e8f0fe 100%);
    color: var(--text-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    background: var(--card-bg);
    border-radius: 20px; /* Reduced border-radius */
    box-shadow: var(--shadow-medium);
    width: 90%;
    max-width: 420px; /* Reduced max-width */
    padding: 24px; /* Reduced padding */
    text-align: center;
    transition: var(--transition);
    margin: 15px; /* Reduced margin */
    min-height: 550px; /* Reduced min-height */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 74, 173, 0.1);
}

/* Blue and Yellow Header Design */
.container::after {
    content: '';
    position: absolute;
    top: 0; /* Changed from 12px to 0 to move the curved design to the top */
    left: 0;
    right: 0;
    height: 80px;
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    opacity: 0.5;
    border-radius: 0 0 50% 50%;
}

.profile {
    width: 120px; /* Reduced size */
    height: 120px; /* Reduced size */
    margin: 0 auto 20px; /* Reduced margin-bottom */
    border-radius: 50%;
    border: 3px solid var(--card-bg);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), /* Adjusted shadow */
                inset 0 0 0 1px rgba(255, 255, 255, 0.6);
    position: relative;
    transition: var(--transition);
    transform: translateY(0);
    z-index: 1;
    aspect-ratio: 1/1;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    padding: 3px; /* Reduced padding */
}

.profile:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 30px rgba(0, 0, 0, 0.15);
}

.profile::after {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), transparent);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.profile:hover::after {
    opacity: 0.15;
    transform: scale(1.1);
}

.status-dot {
    position: absolute;
    bottom: 5px;
    right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    z-index: 2;
    border: 3px solid var(--card-bg);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

/* Logo section removed as requested */

h1 {
    font-size: 26px; /* Reduced font size */
    font-weight: 700;
    margin: 0 0 12px; /* Reduced margin-bottom */
    color: var(--primary-color);
    letter-spacing: -0.02em;
    z-index: 2;
    position: relative;
}

p {
    font-size: 15px; /* Reduced font size */
    margin: 6px 0; /* Reduced margin */
    color: var(--text-secondary);
    font-weight: 500;
}

/* Blue and Yellow Styled Buttons */
button {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border: 2px solid var(--accent-color);
    border-radius: 16px; /* Increased border-radius */
    color: white;
    padding: 20px 36px; /* Increased padding */
    margin: 12px auto; /* Adjusted margin */
    font-size: 16px; /* Increased font size */
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-soft);
    width: 100%;
    max-width: 300px; /* Increased max-width */
    display: block;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: inherit;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.2), transparent);
    transform: translateY(-100%);
    transition: var(--transition);
}

button:hover::before {
    transform: translateY(0);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 74, 173, 0.3);
    border-color: var(--accent-hover);
    background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
}

button:active {
    transform: translateY(0);
}

button:disabled {
    background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
    color: #9CA3AF;
    cursor: not-allowed;
    box-shadow: none;
}

.log {
    margin-top: 8px; /* Reduced margin-top even more */
    font-size: 13px;
    background: rgba(255, 255, 255, 0.5);
    padding: 12px; /* Reduced padding */
    border-radius: 16px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.log p {
    margin: 8px 0; /* Reduced margin */
    padding: 8px 12px; /* Reduced padding */
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.log p:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.log strong {
    color: var(--text-primary);
    font-weight: 600;
    margin-right: 10px;
}

.time-out {
    color: var(--error-color);
    background: rgba(239, 68, 68, 0.05) !important;
}

.message {
    margin-top: 20px; /* Reduced margin-top */
    padding: 16px 18px; /* Reduced padding */
    background: var(--primary-color);
    color: white;
    border: 2px solid var(--accent-color);
    border-radius: 14px; /* Reduced border-radius */
    font-size: 14px; /* Reduced font size */
    font-weight: 500;
    box-shadow: 0 6px 15px rgba(0, 74, 173, 0.2); /* Adjusted shadow */
    animation: slideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    will-change: transform, opacity;
    transform-origin: top;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.message::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.2), transparent);
    transform: translateY(-100%);
    transition: var(--transition);
}

.message:hover::before {
    transform: translateY(0);
}

@keyframes slideIn {
    0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.message.error {
    background: var(--accent-color);
    color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 8px 20px rgba(255, 215, 0, 0.3);
    font-weight: 600;
}

.history-btn {
    background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    margin-top: 6px; /* Reduced margin-top even more */
    border-radius: 14px; /* Reduced border-radius */
    padding: 10px 18px; /* Reduced padding */
    font-size: 13px; /* Reduced font size */
    font-weight: 600;
    transition: var(--transition);
    width: 100%;
    max-width: 180px; /* Reduced max-width */
}

.history-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
    background: linear-gradient(135deg, var(--accent-hover) 0%, var(--accent-color) 100%);
}

.history-btn:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
}

/* Mobile Responsive Design */
@media (max-width: 600px) {
    .container {
        padding: 20px 12px;
        margin: 12px;
        min-height: auto;
    }

    .container::after {
        height: 70px;
    }

    /* Logo removed */

    .profile {
        width: 100px;
        height: 100px;
        margin-bottom: 14px;
        padding: 3px;
    }

    .status-dot {
        width: 14px;
        height: 14px;
        border-width: 2px;
    }

    h1 {
        font-size: 20px;
        margin-bottom: 10px;
    }

    p {
        font-size: 13px;
        margin: 5px 0;
    }

    button {
        padding: 16px 24px; /* Increased padding */
        font-size: 15px; /* Increased font size */
        max-width: 260px; /* Increased max-width */
        border-radius: 14px; /* Increased border-radius */
    }

    .message {
        padding: 14px;
        font-size: 13px;
        margin-top: 18px;
        border-radius: 12px;
    }

    .log {
        padding: 10px; /* Reduced padding even more */
        margin-top: 8px; /* Reduced margin-top even more */
        border-radius: 14px;
    }

    .log p {
        padding: 6px 10px; /* Reduced padding */
        font-size: 12px;
        margin: 6px 0; /* Reduced margin */
    }

    .history-btn {
        margin-top: 8px; /* Reduced margin-top */
        padding: 10px 20px;
        font-size: 13px;
    }
}

@media (max-width: 400px) {
    .container {
        padding: 16px 10px;
        margin: 10px;
    }

    .container::after {
        height: 50px;
    }

    .profile {
        width: 80px;
        height: 80px;
        margin-bottom: 12px;
        padding: 2px;
    }
    
    .status-dot {
        width: 12px;
        height: 12px;
        border-width: 2px;
        right: 5px;
        bottom: 5px;
    }

    h1 {
        font-size: 18px;
    }

    button {
        padding: 12px 18px; /* Increased padding */
        font-size: 14px; /* Increased font size */
        max-width: 180px; /* Increased max-width */
    }

    .log {
        padding: 8px; /* Reduced padding for smallest screens */
        margin-top: 6px; /* Reduced margin-top for smallest screens */
    }

    .log p {
        padding: 4px 8px; /* Reduced padding for smallest screens */
        margin: 4px 0; /* Reduced margin for smallest screens */
    }

    .history-btn {
        margin-top: 6px; /* Reduced margin-top for smallest screens */
        padding: 8px 16px;
        font-size: 13px;
        max-width: 140px;
    }
}

/* Blue and Yellow Styling */
.welcome-text {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.student-info {
    background: rgba(0, 74, 173, 0.08);
    border-radius: 12px;
    padding: 16px;
    margin: 20px 0;
    border-left: 4px solid var(--accent-color);
}

.time-display {
    font-family: 'Courier New', monospace;
    background: var(--accent-color);
    color: var(--primary-color);
    padding: 8px 12px;
    border-radius: 8px;
    display: inline-block;
    margin: 0 4px;
    font-weight: 600;
    border: 1px solid var(--primary-color);
}

/* Button hover effects with blue and yellow */
button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 74, 173, 0.3);
}

button:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(0, 74, 173, 0.2);
}

/* Yellow accent animations */
@keyframes yellowShimmer {
    0% { background-position: -200px 0; }
    100% { background-position: 200px 0; }
}
