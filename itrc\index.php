<?php

    require_once '../core/dbcon.ini';
    require_once '../query/students.qry';
    require_once '../query/users.qry';
	
	$page_title = "ITRC Admin Dashboard";
    $current_page = "Home";

    $studfnc=new STUDENTSFNC();
    $usersfnc = new USERSFNC();

    $allstudents = $studfnc->get_allstudents($db1);
    $totalStudents = count($allstudents);
    $totalUserAccounts = $usersfnc->get_total_useraccounts_count($db1);

    $activeEvents = '5'; // Hardcoded
    $systemHealth = 'Optimal'; // Hardcoded
?>

 <?php include '../assets/common/header.php'; ?>
 <body>
 <?php include '../assets/common/title.php'; ?>

 <div class="main-container" id="main-container"  >
  <?php include '../assets/common/sidebar.php'; ?>
  </div>
  
  <main class="main-wrapper">
	 <?php require '../assets/common/topbar.php'; ?>
	 
	 <section class="section">
        <div class="container-fluid">
          <!-- ========== title-wrapper start ========== -->
          <div class="title-wrapper pt-10">
            <div class="row align-items-center">
              <div class="col-md-6">
                <div class="title">
                  <h2><?php echo isset($page_title) ? $page_title : 'Dashboard'; ?></h2>
                </div>
              </div>
              <!-- end col -->
              <div class="col-md-6">
                <div class="breadcrumb-wrapper">
                  <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                      <li class="breadcrumb-item">
                        <a href="#0">Dashboard</a>
                      </li>
                      <li class="breadcrumb-item active" aria-current="page">
                       <?php echo isset($current_page) ? $current_page : ''; ?>
                      </li>
                    </ol>
                  </nav>
                </div>
              </div>
			 
              <!-- end col -->
            </div>
            <!-- end row -->

            <div class="row">
            <!-- Total Students Card -->
            <div class="col-xl-3 col-lg-4 col-sm-6">
                <div class="icon-card mb-30">
                    <div class="icon purple">
                        <i class="lni lni-users"></i>
                    </div>
                    <div class="content">
                        <h6 class="mb-10">Total Students</h6>
                        <h3 class="text-bold mb-10"><?php echo $totalStudents; ?></h3>
                        <p class="text-sm text-success">
                            Students
                            <span class="text-gray">(Total)</span>
                        </p>
                    </div>
                </div>
            </div>
            <!-- Active Events Card -->
            <div class="col-xl-3 col-lg-4 col-sm-6">
                <div class="icon-card mb-30">
                    <div class="icon success">
                        <i class="lni lni-checkmark-circle"></i>
                    </div>
                    <div class="content">
                        <h6 class="mb-10">Active Events</h6>
                        <h3 class="text-bold mb-10"><?php echo $activeEvents; ?></h3>
                        <p class="text-sm text-success">
                            <i class="lni lni-checkmark-circle"></i> Events
                            <span class="text-gray">(Active)</span>
                        </p>
                    </div>
                </div>
            </div>
            <!-- Total User Accounts -->
            <div class="col-xl-3 col-lg-4 col-sm-6">
                <div class="icon-card mb-30">
                    <div class="icon orange">
                        <i class="lni lni-user"></i>
                    </div>
                    <div class="content">
                        <h6 class="mb-10">Total User Accounts</h6>
                        <h3 class="text-bold mb-10"><?php echo $totalUserAccounts; ?></h3>
                        <p class="text-sm text-success">
                             Users
                            <span class="text-gray">(Total)</span>
                        </p>
                    </div>
                </div>
            </div>
            <!-- System Health -->
            <div class="col-xl-3 col-lg-4 col-sm-6">
                <div class="icon-card mb-30">
                    <div class="icon blue">
                        <i class="lni lni-dashboard"></i>
                    </div>
                    <div class="content">
                        <h6 class="mb-10">System Health</h6>
                        <h3 class="text-bold mb-10"><?php echo $systemHealth; ?></h3>
                        <p class="text-sm text-success">
                            <i class="lni lni-arrow-up"></i> Good
                            <span class="text-gray">(Current)</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        
			 
            <div class="col-12">
                <div class="card-style mb-10">
                    <div class="title d-flex flex-wrap justify-content-between align-items-center">
                        <div class="left">
                            <h6 class="text-medium mb-2">Calendar</h6>
                        </div>
                    </div>
                    <div class="calendar-container" style="width: 100%; min-height: 500px;">
                        <iframe src="https://calendar.google.com/calendar/embed?src=gmccruz%40donbosco.edu.ph&ctz=Asia%2FManila" 
                                style="border: 0; width: 100%; height: 500px;" 
                                frameborder="0" 
                                scrolling="no">
                        </iframe>
                    </div>
                </div>
            </div>
        </div>
          </div>
		</div>
	</section> 
	  <?php include '../assets/common/footer.php'; ?>
	</main>
	  <?php include '../assets/common/scripts.php'; ?>
	 
</body>
</html>
