<?php
class USERSFNC
{
    function get_alluseraccounts($db, $limit, $offset) 
    {
        $data = array();
        $stmt1 = $db->prepare("SELECT * FROM useraccounts LIMIT :limit OFFSET :offset");
        $stmt1->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt1->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt1->execute();
        for($i=1; $i<=$stmt1->rowCount(); $i++)
            $data[] = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function get_total_useraccounts_count($db)
    {
        $stmt = $db->prepare("SELECT COUNT(*) FROM useraccounts");
        $stmt->execute();
        return $stmt->fetchColumn();
    }

    function ins_useraccounts($db, $fullname, $lastname, $firstname, $middlename, $username, $password, $position, $modid)
    {
        $stmt = $db->prepare("INSERT INTO useraccounts (fullname, lastname, firstname, middlename, username, password, position, modid) 
				VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute(array($fullname, $lastname, $firstname, $middlename, $username, $password, $position, $modid));
    }

    function get_allmodule($db) {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM module ORDER BY modid ASC");
        $stmt->execute();
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function upd_useraccounts($db, $uaid, $fullname, $lastname, $firstname, $middlename, $username, $password, $position, $modid, $status)
    {
        $stmt = $db->prepare("UPDATE useraccounts SET fullname = ?, lastname = ?, firstname = ?, middlename = ?, username = ?, password = ?, position = ?, modid = ?, status = ? WHERE uaid = ?");
        $stmt->execute(array($fullname, $lastname, $firstname, $middlename, $username, $password, $position, $modid, $status, $uaid));
    }



    function get_user_by_uaid($db, $uaid) {
        $stmt = $db->prepare("SELECT * FROM useraccounts WHERE uaid = ?");
        $stmt->execute([$uaid]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
	
}
?>
