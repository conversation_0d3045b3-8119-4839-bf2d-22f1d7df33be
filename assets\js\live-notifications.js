async function updateNotificationCount() {
    try {
        const response = await fetch('check_notifications.php');
        const data = await response.json();

        if (data.success) {
            const count = data.notifications ? data.notifications.length : 0;
            const badges = document.querySelectorAll('.notification-count');
            badges.forEach(badge => {
                badge.textContent = count;
                badge.style.display = count > 0 ? 'inline' : 'none';
            });

            // Update tab badges in notifications page if it exists
            if (data.notifications) {
                const timeInNotifs = data.notifications.filter(n => n.type === 'timein');
                const absenceNotifs = data.notifications.filter(n => n.type === 'absence');
                const loginNotifs = data.notifications.filter(n => n.type === 'login');

                // Update Time-in badge
                const timeInBadge = document.querySelector('#time-in-tab .badge');
                if (timeInBadge) {
                    timeInBadge.textContent = timeInNotifs.length;
                    timeInBadge.style.display = timeInNotifs.length > 0 ? 'inline' : 'none';
                }

                // Update Absence badge
                const absenceBadge = document.querySelector('#absence-tab .badge');
                if (absenceBadge) {
                    absenceBadge.textContent = absenceNotifs.length;
                    absenceBadge.style.display = absenceNotifs.length > 0 ? 'inline' : 'none';
                }

                // Update Login badge (for admin dashboard)
                const loginBadge = document.querySelector('#login-tab .badge');
                if (loginBadge) {
                    loginBadge.textContent = loginNotifs.length;
                    loginBadge.style.display = loginNotifs.length > 0 ? 'inline' : 'none';
                }
            }
        }
    } catch (error) {
        console.error('Error updating notification count:', error);
    }
}

function loadDropdownNotifications() {
    const content = document.querySelector('.notifications-dropdown-content');
    if (!content) return;

    content.innerHTML = '<div class="text-center p-3">Loading...</div>';

    fetch('load_notifications.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.notifications.length === 0) {
                    content.innerHTML = '<div class="text-center p-3">No notifications</div>';
                    return;
                }

                const html = data.notifications.map(notification => {
                    let iconClass, icon;
                    if (notification.type === 'login') {
                        iconClass = 'bg-primary';
                        icon = 'fa-user-shield';
                    } else if (notification.type === 'timein') {
                        iconClass = 'bg-success';
                        icon = 'fa-user-clock';
                    } else {
                        iconClass = 'bg-warning';
                        icon = 'fa-exclamation-triangle';
                    }

                    return `
                        <div class="dropdown-item d-flex align-items-center py-2">
                            <div class="mr-3">
                                <div class="icon-circle ${iconClass}">
                                    <i class="fas ${icon} text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="small text-gray-500">${notification.date}</div>
                                ${notification.student_name ? 
                                    `<span class="font-weight-bold">${notification.student_name}</span>` : 
                                    ''}
                                <div class="small text-gray-800">${notification.message}</div>
                            </div>
                        </div>
                    `;
                }).join('');

                content.innerHTML = html;

                if (data.has_more) {
                    content.innerHTML += `
                        <div class="dropdown-item text-center small text-gray-500">
                            <a href="notifications.php">Show all notifications</a>
                        </div>
                    `;
                }
            } else {
                content.innerHTML = '<div class="text-center p-3">Error loading notifications</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            content.innerHTML = '<div class="text-center p-3">Error loading notifications</div>';
        });
}

let notificationQueue = [];
const maxVisibleNotifications = 3;
let visibleNotifications = 0;

function processNotificationQueue() {
    while (notificationQueue.length > 0 && visibleNotifications < maxVisibleNotifications) {
        const {student, message, type} = notificationQueue.shift();
        showLiveNotification(student, message, type);
        visibleNotifications++;
    }
}

async function showLiveNotification(student, message, type) {
    // For time-in notifications, check DND status
    if (type === 'timein') {
        const settings = await getNotificationSettings();
        // If DND is on (!timein_enabled), don't show notification
        if (!settings.timein_enabled) {
            visibleNotifications--; // Decrement since we're not showing it
            return;
        }
    }

    let notificationContainer = document.getElementById('live-notifications');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'live-notifications';
        document.body.appendChild(notificationContainer);
    }

    const notification = document.createElement('div');
    notification.dataset.id = Date.now();
    notification.className = 'notification-toast';
    
    let iconClass, icon;
    
    if (type === 'login') {
        iconClass = 'login';
        icon = 'fa-user-shield';
    } else if (type === 'timein') {
        iconClass = 'time-in';
        icon = 'fa-user-clock';
    } else {
        iconClass = 'warning';
        icon = 'fa-exclamation-triangle';
    }
    
    notification.innerHTML = `
        <div class="notification-content ${iconClass}">
            <div class="notification-icon">
                <i class="fas ${icon}"></i>
            </div>
            <div class="notification-text">
                ${student ? `<h6 class="notification-title">${student}</h6>` : ''}
                <p class="notification-message">${message}</p>
            </div>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="notification-progress"></div>
    `;

    // Play sound for all notification types except when explicitly disabled
    try {
        const audio = new Audio('../assets/notification/notification.mp3');
        audio.volume = 0.5;
        audio.play().catch(error => {
            console.log('Error playing sound:', error);
        });
    } catch (error) {
        console.log('Error creating audio:', error);
    }

    notificationContainer.insertBefore(notification, notificationContainer.firstChild);

    notification.style.animation = 'slideIn 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards';
    notification.classList.add('show');

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards';
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
            visibleNotifications--;
            requestAnimationFrame(() => {
                processNotificationQueue();
            });
        }, 400);
    }, 5000);
}

async function canPlaySound() {
    const settings = await getNotificationSettings();
    return settings.timein_enabled;
}

// Ensure CSS is loaded
function loadNotificationStyles() {
    const cssLink = document.createElement('link');
    cssLink.rel = 'stylesheet';
    cssLink.href = '../assets/css/notifications.css';
    document.head.appendChild(cssLink);
}

// Initialize notifications
document.addEventListener('DOMContentLoaded', () => {
    loadNotificationStyles();
    updateNotificationCount();

    const bellIcon = document.getElementById('alertsDropdown');
    if (bellIcon) {
        bellIcon.addEventListener('click', loadDropdownNotifications);
    }

    // Start periodic checks
    setInterval(checkNewNotifications, 5000);
});

let lastCheckTime = new Date();

async function getNotificationSettings() {
    try {
        const response = await fetch('get_notification_settings.php');
        const data = await response.json();
        return data.success ? data.settings : { timein_enabled: true };
    } catch (error) {
        console.error('Error fetching notification settings:', error);
        return { timein_enabled: true };
    }
}

function groupNotifications(notifications) {
    const groups = {};
    notifications.forEach(notification => {
        const key = notification.type || 'other';
        if (!groups[key]) {
            groups[key] = [];
        }
        groups[key].push(notification);
    });
    return groups;
}

function formatGroupedNotification(notifications, type) {
    if (notifications.length === 1) {
        return {
            student: notifications[0].student_name,
            message: notifications[0].message,
            type
        };
    }

    const studentNames = notifications.map(n => n.student_name).filter(Boolean);
    if (studentNames.length === 0) {
        return {
            student: null,
            message: notifications[0].message,
            type
        };
    }

    const lastStudent = studentNames.pop();

    if (type === 'timein') {
        return {
            student: 'Multiple Students',
            message: `${studentNames.join(', ')} and ${lastStudent} recorded time-in`,
            type
        };
    } else if (type === 'login') {
        return {
            student: 'Multiple Staff',
            message: `${studentNames.join(', ')} and ${lastStudent} logged in`,
            type
        };
    }

    return {
        student: 'Multiple Users',
        message: `${studentNames.join(', ')} and ${lastStudent}: ${notifications[0].message}`,
        type
    };
}

async function checkNewNotifications() {
    try {
        // Check settings first
        const settings = await getNotificationSettings();
        
        const response = await fetch('check_notifications.php');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        if (!data.success) {
            throw new Error(data.message || 'Failed to fetch notifications');
        }

        const currentTime = new Date();

        if (data.notifications && data.notifications.length > 0) {
            const newNotifications = data.notifications.filter(notification => {
                const notificationTime = new Date(notification.created_at);
                return notificationTime > lastCheckTime;
            });

            if (newNotifications.length > 0) {
                const groups = groupNotifications(newNotifications);

                Object.entries(groups).forEach(([type, notifications]) => {
                    if (notifications.length > 1) {
                        notificationQueue.push(formatGroupedNotification(notifications, type));
                    } else {
                        notificationQueue.push({
                            student: notifications[0].student_name,
                            message: notifications[0].message,
                            type
                        });
                    }
                });

                processNotificationQueue();
                updateNotificationCount();
            }
        }

        lastCheckTime = currentTime;
    } catch (error) {
        if (error.message === 'Not logged in') {
            window.location.href = '../index.php';
            return;
        }
        
        console.error('Error checking notifications:', error);
        
        if (window.notificationCheckInterval) {
            clearInterval(window.notificationCheckInterval);
            window.notificationCheckInterval = null;
        }
    }
}

function clearAllNotifications(event) {
    event.preventDefault();

    fetch('clear_notifications.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const content = document.querySelector('.notifications-dropdown-content');
            if (content) {
                content.innerHTML = '<div class="text-center p-3">No notifications</div>';
            }

            const badges = document.querySelectorAll('.notification-count');
            badges.forEach(badge => {
                badge.textContent = '0';
                badge.style.display = 'none';
            });

            const clearSection = document.querySelector('.text-center.mt-4');
            if (clearSection) {
                clearSection.style.display = 'none';
            }
        }
    })
    .catch(error => console.error('Error:', error));
}
